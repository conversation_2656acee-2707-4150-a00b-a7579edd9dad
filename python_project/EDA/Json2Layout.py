'''
将json文件中的状态同步至SailWind Layout中
注意：需要在SailWind Layout中打开对应的布局文件
'''
import sys
import json
import win32com.client
from BaseDefine import *

if __name__=='__main__':
    if len(sys.argv) < 3:
        # 示例: python .\Json2Layout.py D:\\data\model_output_json\\human_A00BC051A0_output.json 2
        # unit 1: mil, 2: mm
        print("Usage: python Json2Layout.py <json_file> <unit>")
        sys.exit(1)
    
    json_file = sys.argv[1]
    if not json_file.endswith('.json'):
        print("Error: The input file must be a JSON file.")
        sys.exit(1)
    unit = int(sys.argv[2])
    
    # 解析Component信息
    with open(json_file, 'r', encoding="utf-8") as file:
        data = json.load(file)
    compInfos = data[COMPONENTS_KEY]
    comps = {}
    pinInfos = {}
    for compInfo in compInfos.values():
        compName = compInfo[COMPNAME_KEY]
        compObj = Component(compInfo)
        comps[compName] = compObj

    pcbApp = win32com.client.Dispatch('SailWindLayout.Application')
    pcbDoc = pcbApp.ActiveDocument
    if pcbDoc is None:
        print("Error: No active PCB document found.")
        sys.exit(1)
        
    # 获取top、bottom层的index
    topIndex = 1
    bottomIndex = 2
    for layer in pcbDoc.Layers:
        layerName = layer.Name
        if layerName.lower() == 'top':
            topIndex = layer.Number
        elif layerName.lower() == 'bottom':
            bottomIndex = layer.Number
    
    # 将位置转换为布局单位
    if unit == 1:  # mil
        pcbDoc.unit = 2
    elif unit == 2:  # mm
        pcbDoc.unit = 4

    for comp in pcbDoc.Components:
        compName = comp.Name
        comp_obj = comps.get(compName)
        if comp_obj is None:
            print(f"Warning: Component {compName} not found in the design.")
            continue

        dx = comp.PositionX - comp.CenterX
        dy = comp.PositionY - comp.CenterY
        try:
            comp.Move(comp_obj.pos.x + dx, comp_obj.pos.y + dy)
            comp.Orientation = comp_obj.orientation
            # todo : layer需要转换为布局层
            if comp_obj.layer == LayerType.Top:
                comp.Layer = topIndex
            elif comp_obj.layer == LayerType.Bottom:
                comp.Layer = bottomIndex
            else:
                comp.Layer = topIndex
        except Exception as e:
            print(f"Error moving component {compName}: {e}")
            continue    