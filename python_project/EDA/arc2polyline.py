import numpy as np


def arc_polygon(start, end, angle_deg, num_points=100):
    """
    通过起点、终点和旋转角度，生成近似多边形点序列（折线近似圆弧）
    :param start: (x0, y0) 起点
    :param end: (x1, y1) 终点
    :param angle_deg: 圆心角（度），正为逆时针，负为顺时针
    :param num_points: 多边形顶点数
    :return: [(x, y), ...] 点序列
    """
    x0, y0 = start
    x1, y1 = end
    dx, dy = x1 - x0, y1 - y0
    chord = np.hypot(dx, dy)
    if abs(angle_deg) < 1e-6:
        # 角度为0，直接连线
        return [start, end]
    angle_rad = np.deg2rad(angle_deg)
    mx, my = (x0 + x1) / 2, (y0 + y1) / 2
    r = chord / (2 * np.sin(abs(angle_rad) / 2))
    theta = np.arctan2(dy, dx)
    h = r * np.cos(angle_rad / 2)
    sign = 1 if angle_deg > 0 else -1
    perp_theta = theta + sign * np.pi / 2
    cx = mx + h * np.cos(perp_theta)
    cy = my + h * np.sin(perp_theta)
    start_angle = np.arctan2(y0 - cy, x0 - cx)
    end_angle = start_angle + angle_rad
    points = []
    for t in np.linspace(0, 1, num_points):
        a = start_angle + t * (end_angle - start_angle)
        x = cx + r * np.cos(a)
        y = cy + r * np.sin(a)
        points.append((x, y))
    return points


def CheckSamePoint(point1, point2):
    x = abs(point1[0] - point2[0])
    y = abs(point1[1] - point2[1])
    return x < 0.0001 and y < 0.0001


def GetPolygon(polyline, numPoints=100):
    """

    :param polyline: json 中的边
    :param numPoints: 对弧段切分点数
    :return: 若不闭合返回 []，闭合返回整个封闭多边形点链
    """
    closed = True
    if not CheckSamePoint(polyline[0], polyline[-1]):
        closed = False
    result = []
    for i in range(len(polyline) - 1):
        start = polyline[i]
        end = polyline[i + 1]
        startPoint = (start[0], start[1])
        endPoint = (end[0], end[1])
        angle_deg = start[2]
        points = arc_polygon(startPoint, endPoint, angle_deg, num_points=numPoints)
        result += points
    return result, closed
