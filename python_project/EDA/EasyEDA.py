import json
import math
import os
from collections import defaultdict

import matplotlib.patches as patches
import matplotlib.pyplot as plt
from matplotlib.collections import PatchCollection

import numpy as np

from BaseDefine import *
# from BaseDefine import *
from LineDetect import LineDetect

from MergeUnclosed import MergeAreaInJson

PLACEANALYZER = 0   # 1 则将所有 gap = 0; 0 则使用既定规则


class EasyEDA:
    def __init__(self):
        self.pcbName = None
        self.mainComp = None  # 处理时，可以只取第一个器件

        self.comps = {}              # compName : class Component
        self.nets = {}               # netName : class Net
        self.pinName2netName = {}    # pin name : net name
        self.board: Box = Box(0,0,0,0)

        self.boardArea = {}         # areaId: class Area
        self.keepoutArea = {}       # areaId: class Area

    def ConfirmOutline(self):
        """
        对 self.boardArea 进一步处理，删除不闭合板框，区分挖空区域;
        对 boardArea 根据外包围框坐标进行去重，删除位于 outline 外的无效板框
        方法：比较半周长，选择最大半周长的为挖空区域
        :return:
        """
        if len(self.boardArea) == 0:
            print("boardError: 不存在闭合板框")
            return
        # 对板框进行去重
        delSameAreaDict = defaultdict(list)
        for areaId, board in self.boardArea.items():
            delSameAreaDict[(board.top, board.bottom, board.left, board.right)].append(areaId)
        tempBoardArea = {}
        for box, areaIds in delSameAreaDict.items():
            tempBoardArea[areaIds[0]] = self.boardArea[areaIds[0]]

        # 计算各板框内器件数量，选择最多的为 outline
        area2CompsDict = defaultdict(list)
        for compName, compObj in self.comps.items():
            compRect = compObj.CalcPickBox()
            cur_top, cur_bottom, cur_left, cur_right = (compRect.top,
                                                        compRect.bottom,
                                                        compRect.left,
                                                        compRect.right)
            for areaId, boardObj in tempBoardArea.items():
                if not boardObj.closed:
                    # 忽略非闭合板框
                    continue
                outline_top, outline_bottom, outline_left, outline_right = (boardObj.top,
                                                                            boardObj.bottom,
                                                                            boardObj.left,
                                                                            boardObj.right)
                if not (cur_top < outline_bottom or cur_bottom > outline_top or cur_left > outline_right or cur_right < outline_left):
                    # 与当前区域交叉
                    area2CompsDict[areaId].append(compName)
        if len(area2CompsDict) == 0:
            print("boardError: 不存在有效板框 outline")
            return

        # 根据含有器件的数量进行降序排序，确定板框为包含器件数量最多的 Area
        sorted_keys = sorted(area2CompsDict, key=lambda k: len(area2CompsDict[k]), reverse=True)
        maxAreaId = sorted_keys[0]
        tempBoardArea[maxAreaId].isOutline = True

        # 删除位于板框外的区域
        delOutOfBoard = {}
        outline_top, outline_bottom, outline_left, outline_right = (tempBoardArea[maxAreaId].top,
                                                                    tempBoardArea[maxAreaId].bottom,
                                                                    tempBoardArea[maxAreaId].left,
                                                                    tempBoardArea[maxAreaId].right)
        for areaId, boardObj in tempBoardArea.items():
            if areaId == maxAreaId:
                delOutOfBoard[areaId] = boardObj
                continue
            cur_top, cur_bottom, cur_left, cur_right = (boardObj.top,
                                                        boardObj.bottom,
                                                        boardObj.left,
                                                        boardObj.right)
            if (outline_bottom - cur_top >= -0.0001 or
                    cur_bottom - outline_top >= -0.0001 or
                    cur_left - outline_right >= -0.0001 or
                    cur_right - outline_left <= 0.0001):
                # 忽略位于板框外的区域
                continue
            delOutOfBoard[areaId] = boardObj
        self.boardArea = delOutOfBoard

    def InitEnv(self, path):
        """
        从文件系统中获取json文件，并初始化环境
        :param path: 文件路径
        :return: null
        """
        with open(path, 'r', encoding="utf-8") as file:
            data = json.load(file)

        if PCBNAME_KEY in data:
            self.pcbName = data[PCBNAME_KEY]

        # 解析Component信息
        compInfos = data[COMPONENTS_KEY]
        pinInfos = {}
        for compInfo in compInfos.values():
            compName = compInfo[COMPNAME_KEY]
            # if compName[0] == 'X':
            #     continue
            compObj = Component(compInfo)
            for pinName, pinObj in compObj.pins.items():
                pinInfos[pinName] = pinObj
                self.pinName2netName[pinName] = None

            self.comps[compName] = compObj

        # 解析 Area 信息
        if AREA_KEY in data.keys():
            areaInfos = data[AREA_KEY]
            newAreaDict = MergeAreaInJson(areaInfos)    # 对Area执行合并
            for areaId, areaInfo in newAreaDict.items():
                areaObj = Area(areaId, areaInfo)
                if not areaObj.closed:
                    continue
                if areaObj.areaType == BOARD_TYPE:
                    self.boardArea[areaId] = areaObj
                else:
                    self.keepoutArea[areaId] = areaObj
        self.ConfirmOutline()   # 确定板框并删除无效板框

        # 解析Net信息
        for netName, pins in data[NETS_KEY].items():
            pinList = []
            for pin in pins:
                pinName = pin[0]
                self.pinName2netName[pinName] = netName
                pinObj = pinInfos[pinName]
                pinList.append(pinObj)
            net = Net(netName, pinList)
            self.nets[netName] = net

        # 解析模块信息
        if GROUP_KEY in data.keys():
            groups = data[GROUP_KEY]
            for schName in groups:
                for moduleName, moduleInfo in groups[schName].items():
                    key = moduleInfo['key']
                    if len(key) > 0:
                        self.mainComp = key[0]

        self.board = self.CalcBoardBox()

    def GetAllCompName(self) -> list[str]:
        """
        获取全部器件的名字
        :return: 
        """
        return list(self.comps.keys())

    def GetMainCompName(self) -> str:
        """
        获取关键器件名称
        :return: 关键器件名称
        """
        return self.mainComp

    def GetComp(self, name) ->  Component | None:
        """
        获取器件对象
        :param name: 器件名称
        :return: Component对象
        """
        if name in self.comps:
            return self.comps[name]

        return None
    
    def CalcTotalLength(self) -> float :
        """
        计算总的飞线长度，按照曼哈顿距离进行计算
        :return: 飞线总长度
        """
        totalLength = 0.0
        for net in self.nets.values():
            if self.IsGNDorPowerNet(net.netName):
                continue

            totalLength += net.CalcNetLength()

        return totalLength

    def CalcTotalCrossPoint(self) -> list[Point]:
        """
        计算总的交叉点
        :return: 交叉点的坐标
        """
        lines = []
        for net in self.nets.values():
            if self.IsGNDorPowerNet(net.netName):
                continue

            lines += net.GetAllLine()

        lineDetect = LineDetect()
        return lineDetect.CalcAllCrossPoint(lines)
    
    def CalcOverlapArea(self) -> float:
        """
        计算重叠面积
        :return : 重叠面积
        """
        overlapArea = 0
        comps = list(self.comps.values())
        num_components = len(comps)
        for i in range(num_components):
            for j in range(i + 1, num_components):
                comp1 = comps[i]
                comp2 = comps[j]
                # or (comp1.name == self.mainComp or comp2.name == self.mainComp)
                if comp1.layer == comp2.layer:
                    comp1Box = comp1.CalcPickBox()
                    comp2Box = comp2.CalcPickBox()

                    # 增加工艺间距
                    gap = self._get_gap(comp1.name, comp2.name)
                    if PLACEANALYZER:       # 仅在布局结果分析时使用
                        gap = 0
                    comp1Box.Expand(gap)

                    overlapArea += comp1Box.GetIntersectionArea(comp2Box)
        return overlapArea

    def Calcperimeter(self) -> float:
        """
        计算器件外接矩形的半周长
        :return: 半周长
        """
        box = self._calc_comps_box()
        return box.GetWidth() + box.GetHeight()

    
    def CalcHWPL(self) -> float:
        """
        计算半周线长
        :return : 半周线长
        """
        total_hwpl = 0
        for net in self.nets.values():
            if self.IsGNDorPowerNet(net.netName):
                continue
            
            total_hwpl += net.CalcNetHPWL()
        return total_hwpl

    def UpdateNet(self):
        """
        更新网络的拓扑关系，如果引脚位置发生变换，计算线长或者交叉点前需调用
        """
        for net in self.nets.values():
            net.UptateNetTopology()

    def is_in_board(self, comp_name) -> bool:
        """
        判断是否在板框内，仅针对模块布局场景
        :param comp_name:
        :return:
        """
        if comp_name in self.comps:
            comp_obj = self.comps[comp_name]
            rect = comp_obj.CalcPickBox()
            if self.board.Contains(rect):
                return True
        return False

    def CalcBoardBox(self, expandSiz=0):
        """
        计算板子的外包围框，由器件、keepout、board 最小外包围矩形决定
        :param expandSiz: 针对模块布局，对外包围框进行扩展
        :return: 板子的外包围框
        """
        compBox = self._calc_comps_box()
        areaBox = self._calc_areas_box()
        rect_top = max(compBox.top, areaBox.top)
        rect_bottom = min(compBox.bottom, areaBox.bottom)
        rect_left = min(compBox.left, areaBox.left)
        rect_right = max(compBox.right, areaBox.right)
        boardBox = Box(rect_top, rect_bottom, rect_left, rect_right)
        boardBox.Expand(expandSiz)

        return boardBox

    def MoveComp(self, name, pos : Point):
        """
        平移器件
        :param name:
        :param pos:
        :return:
        """
        if name in self.comps:
            compObj = self.comps[name]
            # 更新Pin信息
            dx = pos.x - compObj.pos.x
            dy = pos.y - compObj.pos.y

            for pinObj in compObj.pins.values():
                point = pinObj.pos

                point = self.translatePoint(point, dx, dy)
                pinObj.pos = point
                self.UpdateNetPinInfo(pinObj)

            self.comps[name].pos = pos

    def RotateComp(self, name, ori):
        """
        旋转器件
        :param name:
        :param ori: 旋转角度,角度值
        :return:
        """
        if math.isclose(ori,0.0,abs_tol=1e-5):
            return
        
        if ori < 0:
            ori = 360 + ori
        
        if name in self.comps:
            compObj = self.comps[name]

            for pinObj in compObj.pins.values():
                point = pinObj.pos

                # 旋转
                pinObj.pos = self.rotatePoint(point, ori * (math.pi / 180), compObj.pos)
                self.UpdateNetPinInfo(pinObj)

            self.comps[name].orientation += ori
            self.comps[name].orientation = self.comps[name].orientation % 360

            # todo : 目前仅支持90度旋转，后续需要支持45度
            if (45 <= ori <= 90) or (235 <= ori <= 315):
                compObj.boxSize.rotate()

    def FlipComp(self, name, layer : LayerType):
        """
        器件镜像
        :return:
        """
        if name in self.comps:
            compObj = self.comps[name]
            compObj.layer = layer

            for pinObj in compObj.pins.values():
                point = pinObj.pos
                pinObj.pos = self.flipPoint(point, compObj.pos)
                self.UpdateNetPinInfo(pinObj)

    def drawPolygon(self, points, fillFlag=False, color='black', linewidth=1):
        xs, ys = zip(*points)
        plt.plot(xs, ys, color=color, linewidth=linewidth)
        if fillFlag:
            plt.fill(
                xs, ys,
                edgecolor=color,
                facecolor='none',
                hatch='////',  # 斜线填充（45°），'\\' 为 135°
                linewidth=linewidth
            )

    def Show(self, isShowLink = True, isShowPower = False,
             isShowBoard = False, isShowKeepout=False, isShowComps=True,
             saveFlag=False, imageFolder=os.getcwd(),
             drawLayer=LayerType.All, fileIndex=None
             ):
        """
        可视化当前布局，目前仅可作为调试函数使用
        :param fileIndex: 图片名称，若为 None，图片名称为 pcbName_layerName.png
        :param isShowLink: 是否显示飞线，默认不显示
        :param isShowPower: 是否显示电源网络，默认不显示
        :param isShowBoard: 是否显示板框，默认不显示
        :param isShowKeepout: 是否显示禁布区，默认不显示
        :param saveFlag: 是否保存图片，默认不保存
        :param imageFolder: 保存图片目录，若未指定，目录为当前执行目录
        :param drawLayer: 绘制图片的层，默认为双面
        :return: null
        """

        if saveFlag:
            hor = self.board.right - self.board.left
            ver = self.board.top - self.board.bottom
            # 避免画幅过大
            while hor // 100 > 1 or ver // 100 > 1:
                hor = hor / 10
                ver = ver / 10

            # 创建图形和坐标轴
            fig, ax = plt.subplots(figsize=(hor, ver))

            boardLineWidth = 6
            keepoutLineWidth = 2
            spacing = 5
        else:
            fig, ax = plt.subplots()
            boardLineWidth = 2
            keepoutLineWidth = 1
            spacing = 1

        textDict = {
            LayerType.Top: [],
            LayerType.Bottom: []
        }

        # 绘制器件
        top_rects = []          # top面器件
        bot_rects = []          # bottom面器件
        for comp in self.comps.values():
            name = comp.name
            width = comp.boxSize.width
            height = comp.boxSize.height
            centerX = comp.pos.x
            centerY = comp.pos.y
            layer = comp.layer

            # 判断器件所在的层
            if LayerType.Top == layer:
                top_rects.append(patches.Rectangle((centerX - width / 2, centerY - height / 2), width, height))
                textDict[LayerType.Top].append([centerX, centerY, name])
            else:
                bot_rects.append(patches.Rectangle((centerX - width / 2, centerY - height / 2), width, height))
                textDict[LayerType.Bottom].append([centerX, centerY, name])


        # 绘制网络
        if isShowLink:
            for net in self.nets.values():
                if not isShowPower and self.IsGNDorPowerNet(net.netName):
                    continue

                for pinPair in net.pinPairs:
                    pin_obj_1 = net.pins[pinPair[0]]
                    pin_obj_2 = net.pins[pinPair[1]]
                    # 绘制飞线
                    ax.plot([pin_obj_1.pos.x, pin_obj_2.pos.x], [pin_obj_1.pos.y, pin_obj_2.pos.y], color='green', linewidth=1, linestyle='--')
                    # 绘制引脚
                    ax.plot(pin_obj_1.pos.x, pin_obj_1.pos.y, 'o', color='red', markersize=2)
                    ax.text(pin_obj_1.pos.x, pin_obj_1.pos.y, pin_obj_1.pinName, fontsize=7, color='red')
                    ax.plot(pin_obj_2.pos.x, pin_obj_2.pos.y, 'o', color='red', markersize=2)
                    ax.text(pin_obj_2.pos.x, pin_obj_2.pos.y, pin_obj_2.pinName, fontsize=7, color='red')

        # 绘制板框
        if isShowBoard:
            for boardId, board in self.boardArea.items():
                if board.isOutline:
                    boardColor = 'black'
                else:
                    boardColor = 'indigo'

                if board.polyLineType == PolyType.Polyline:
                    self.drawPolygon(board.polyLines, color=boardColor, linewidth=boardLineWidth)
                elif board.polyLineType == PolyType.Circle:
                    circle = plt.Circle((board.circle[0], board.circle[1]), board.circle[2],
                                        edgecolor=boardColor,  # 边框颜色
                                        facecolor='none',  # 无填充色
                                        linewidth=boardLineWidth)  # 边框宽度
                    # 将圆添加到坐标轴
                    ax.add_patch(circle)

        # 设置绘制层
        layers = [drawLayer]
        if drawLayer == LayerType.All:
            layers = [LayerType.Top, LayerType.Bottom]

        # 绘制禁布区
        if isShowKeepout:
            for keepoutId, keepout in self.keepoutArea.items():
                if keepout.layer in layers or keepout.layer == LayerType.All:
                    if keepout.polyLineType == PolyType.Polyline:
                        self.drawPolygon(keepout.polyLines, fillFlag=True, color='pink', linewidth=keepoutLineWidth)
                    elif keepout.polyLineType == PolyType.Circle:
                        circle = plt.Circle(
                            (keepout.circle[0], keepout.circle[1]), keepout.circle[2],
                            edgecolor='pink',  # 边框颜色
                            facecolor='none',  # 无填充色
                            hatch='\\\\\\',  # 斜线填充（45° 或 135°）
                            linewidth=keepoutLineWidth,  # 边框宽度
                        )
                        # 将圆添加到坐标轴
                        ax.add_patch(circle)

        # 绘制器件图形与编号
        if isShowComps:
            for layerId in layers:
                if layerId == LayerType.Top:
                    top_collection = PatchCollection(top_rects, alpha=0.4, edgecolor='b', facecolor='blue')
                    ax.add_collection(top_collection)
                    for textInfo in textDict[layerId]:
                        ax.text(textInfo[0], textInfo[1], textInfo[2], ha='center', va='center', color='black', fontsize=8)
                if layerId == LayerType.Bottom:
                    bot_collection = PatchCollection(bot_rects, alpha=0.4, edgecolors='yellow', facecolors='yellow')
                    ax.add_collection(bot_collection)
                    for textInfo in textDict[layerId]:
                        ax.text(textInfo[0], textInfo[1], textInfo[2], ha='center', va='center', color='black', fontsize=8)

        # 设置坐标轴范围，保持比例尺一样
        plt.axis('equal')

        # 动态计算器件的坐标范围
        all_x = []
        all_y = []
        for comp in self.comps.values():
            width = comp.boxSize.width
            height = comp.boxSize.height
            centerX = comp.pos.x
            centerY = comp.pos.y
            all_x.extend([centerX - width / 2, centerX + width / 2])
            all_y.extend([centerY - height / 2, centerY + height / 2])

        # 检查是否有器件数据
        if not all_x or not all_y:
            print("警告: 没有找到器件坐标数据")
            return figurePath

        # 更新坐标轴范围，确保包含所有器件
        min_x, max_x = min(all_x), max(all_x)
        min_y, max_y = min(all_y), max(all_y)
        print(f"器件坐标范围: X({min_x}, {max_x}), Y({min_y}, {max_y})")  # 调试信息
        print(f"板框范围: Left({self.board.left}), Right({self.board.right}), Top({self.board.top}), Bottom({self.board.bottom})")  # 调试信息

        # 直接使用器件的坐标范围设置显示区域，并添加更大的间距
        margin = max((max_x - min_x) * 0.1, (max_y - min_y) * 0.1, spacing * 10)  # 动态计算边距
        ax.set_xlim(min_x - margin, max_x + margin)
        ax.set_ylim(min_y - margin, max_y + margin)

        plt.grid(True)



        figurePath=""
        # 显示图形
        if saveFlag:
            if not os.path.exists(imageFolder):
                os.makedirs(imageFolder)
            if fileIndex is None:
                figurePath = imageFolder + '/' + self.pcbName.split('.')[0] + '_' + str(drawLayer).split('.')[-1] + '.png'
            else:
                figurePath = imageFolder + '/' + fileIndex + '.png'

            plt.savefig(figurePath, facecolor=fig.get_facecolor())
            plt.close()
            print(f"图片已保存至: {figurePath}")
        else:
            plt.show()
        return figurePath


    def rotatePoint(self, point, angle, center):
        """
        绕指定点旋转一个点
        :param point: 要旋转的点的坐标 (x, y)
        :param angle: 旋转角度（弧度）
        :param center: 旋转中心点的坐标 (cx, cy)
        :return: 旋转后的点 (x', y')
        """
        x, y = point.x, point.y
        cx, cy = center.x, center.y
        cos_theta = np.cos(angle)
        sin_theta = np.sin(angle)

        # 计算旋转后的平移分量
        tx = cx * (1 - cos_theta) + cy * sin_theta
        ty = -cx * sin_theta + cy * (1 - cos_theta)

        # 构造绕指定点旋转的变换矩阵
        rotation_matrix = np.array([
            [cos_theta, -sin_theta, tx],
            [sin_theta, cos_theta, ty],
            [0, 0, 1]
        ])

        # 将点转换为齐次坐标
        point_homogeneous = np.array([x, y, 1])

        # 计算旋转后的点
        rotated_point = np.dot(rotation_matrix, point_homogeneous)

        # 返回二维坐标
        return Point(rotated_point[0], rotated_point[1])

    def translatePoint(self, point, tx, ty):
        """
        平移一个点
        :param point: 点的坐标 (x, y)
        :param tx: 沿 x 轴的平移量
        :param ty: 沿 y 轴的平移量
        :return: 平移后的点 (x', y')
        """
        return Point(point.x + tx, point.y + ty)

    def flipPoint(self, point, center=None):
        """
        镜像翻转一个点（绕指定中心点）
        :param point: 要翻转的点的坐标 (x, y)
        :param center: 翻转中心点坐标 (cx, cy)，默认为原点 (0,0)
        :return: 翻转后的点 (x', y')
        """
        x, y = point.x, point.y
        if center is None:
            cx, cy = 0, 0
        else:
            cx, cy = center.x, center.y

        # 构造镜像变换矩阵
        # 水平翻转（关于垂直轴对称）
        flip_matrix = np.array([
            [-1, 0, 2 * cx],  # x' = -x + 2cx → 绕x=cx对称
            [0, 1, 0],
            [0, 0, 1]
        ])

        # 齐次坐标变换
        point_homogeneous = np.array([x, y, 1])
        flipped_point = np.dot(flip_matrix, point_homogeneous)

        return Point(flipped_point[0], flipped_point[1])

    def UpdateNetPinInfo(self, pinObj):
        """
        更新网络中的信息
        :param pinObj: 需要更新的pin对象
        :return:
        """
        pinName = pinObj.pinName
        netName = self.pinName2netName[pinName]
        if netName in self.nets:
            self.nets[netName].SetPinInfo(pinObj)

    def IsGNDorPowerNet(self, net_name):
        """
        判断是否是电源网络或者地网络
        :param net_name:
        :return: True : 电源网络或者地网络，
                 False : 非电源网络或者地网络
        """
        return 'gnd' in net_name.lower()
    
    def _get_type(self, name):
        # 粗略地对不同元件进行分组
        if name[0] == 'U' or name[0] == 'J':
            return 0
        if name[0] == 'C' or name[0] == 'R':
            return 1
        else:
            return 2

    def _get_gap(self, name1, name2):
        """
         获取工艺间距
        :param name1: 元件名
        :param name2:
        :return:
        """
        type1 = self._get_type(name1)
        type2 = self._get_type(name2)
        if type1 == 0 and type2 == 0:
            return 20
        elif type1 * type2 == 0:
            return 20
        else:
            return 10

    def _calc_comps_box(self):
        rect_top = float('-inf')
        rect_right = float('-inf')
        rect_bottom = float('inf')
        rect_left = float('inf')

        for key in self.comps:
            width = self.comps[key].boxSize.width
            height = self.comps[key].boxSize.height
            centerX = self.comps[key].pos.x
            centerY = self.comps[key].pos.y

            # 计算坐标范围
            if (centerX + width / 2) > rect_right :
                rect_right = centerX + width / 2
            if (centerX - width / 2) < rect_left:
                rect_left = centerX - width / 2
            if (centerY + height / 2) > rect_top:
                rect_top = centerY + height / 2
            if (centerY - height / 2) < rect_bottom:
                rect_bottom = centerY - height / 2

        return Box(rect_top, rect_bottom, rect_left, rect_right)

    def _calc_areas_box(self):
        top = float('-inf')
        right = float('-inf')
        bottom = float('inf')
        left = float('inf')
        for areaId, areaObj in self.boardArea.items():
            bottom = min(bottom, areaObj.bottom)
            top = max(top, areaObj.top)
            right = max(right, areaObj.right)
            left = min(left, areaObj.left)
        for areaId, areaObj in self.keepoutArea.items():
            bottom = min(bottom, areaObj.bottom)
            top = max(top, areaObj.top)
            right = max(right, areaObj.right)
            left = min(left, areaObj.left)
        return Box(top, bottom, left, right)
