from EasyEDA import *
import os
import json

def main():
    # 1. 指定工作目录
    work_dir = r"E:/迅雷下载/110份pads_处理后xjx"
    
    # 验证目录是否存在
    if not os.path.isdir(work_dir):
        print(f"错误：目录 {work_dir} 不存在")
        return
    

    eda = EasyEDA()

    # 2. 遍历工作目录下的所有子目录
    for entry in os.scandir(work_dir):
        if entry.is_dir():
            project_dir_name = entry.name
            project_dir_path = os.path.join(work_dir, project_dir_name)
            json_dir_path = os.path.join(work_dir, project_dir_name, "JSON")
            
            # 3. 检查是否存在JSON目录
            if os.path.isdir(json_dir_path):
                print(f"发现JSON目录：{json_dir_path}")
                
                # 遍历JSON目录中的文件
                for json_file in os.scandir(json_dir_path):
                    if json_file.is_file() and json_file.name.endswith('.json'):
                        file_path = os.path.join(json_dir_path, json_file.name)
                        new_file_path = os.path.join(project_dir_path, json_file.name)

                        # 移动json文件到工程目录下
                        if not os.path.exists(new_file_path):
                            os.rename(file_path, new_file_path)

                        eda.InitEnv(new_file_path)
                        # 4. 调用处理函数
                        eda.Show(isShowBoard = True, isShowKeepout=True, saveFlag=True, imageFolder=project_dir_path)


if __name__ == "__main__":
    main()



