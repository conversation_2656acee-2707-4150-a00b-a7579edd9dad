from EasyEDA import *
import os
import shutil

def delete():
    # 1. 指定工作目录
    work_dir = r"E:/迅雷下载/已提取"
    
    # 验证目录是否存在
    if not os.path.isdir(work_dir):
        print(f"错误：目录 {work_dir} 不存在")
        return
    

    eda = EasyEDA()

    # 2. 遍历工作目录下的所有子目录
    for entry in os.scandir(work_dir):
        if entry.is_dir():
            project_dir_name = entry.name
            project_dir_path = os.path.join(work_dir, project_dir_name)

            for item in os.scandir(project_dir_path):
                item_path = os.path.join(project_dir_path, item.name)
                if item.is_dir():
                    shutil.rmtree(item_path)
                elif entry.is_file():
                    if item_path.endswith('.json')  or item_path.endswith('.png') or item_path.endswith('.svg'):
                        continue
                    os.remove(item_path)


def check():
    # 1. 指定工作目录
    work_dir = r"E:/迅雷下载/已提取"
    save_dir = r"E:/迅雷下载/done"
    # 验证目录是否存在
    if not os.path.isdir(work_dir):
        print(f"错误：目录 {work_dir} 不存在")
        return

    # 2. 遍历工作目录下的所有子目录
    for entry in os.scandir(work_dir):
        if entry.is_dir():
            project_dir_name = entry.name
            project_dir_path = os.path.join(work_dir, project_dir_name)
            save_dir_path = os.path.join(save_dir, project_dir_name)

            json_flag = False
            png_flag = False
            for item in os.scandir(project_dir_path):
                item_path = os.path.join(project_dir_path, item.name)

                
                if item.is_file():
                    if item_path.endswith('.json'):
                        json_flag = True
                    elif item_path.endswith('.png'):
                        png_flag = True
                    
                    if json_flag and png_flag:
                        if not os.path.exists(save_dir_path):
                            shutil.copytree(project_dir_path, save_dir_path)


def rename():
    work_dir =  r"E:/迅雷下载/done"
    
    i = 0
    for entry in os.scandir(work_dir):
        if entry.name.startswith("case"):
            continue

        i += 1
        new_name = f"case{i}"
        
        old_path = entry.path
        new_path = os.path.join(work_dir, new_name)
        
        try:
            os.rename(old_path, new_path)
            print(f"Renamed: {entry.name} -> {new_name}")
        except Exception as e:
            print(f"Error renaming {entry.name}: {str(e)}")


def retry_faild_case():
    work_dir =  r"110份pads_处理后xjx"
    

if __name__ == "__main__":
    rename()



