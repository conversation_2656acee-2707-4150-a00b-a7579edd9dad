import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from matplotlib.patches import Rectangle

from BaseDefine import Point
from EasyEDA import EasyEDA

class EDAForceLayout:
    def __init__(self, json_file):
        """初始化布局器，使用EDA解析JSON文件"""
        self.eda = EasyEDA()
        self.eda.InitEnv(json_file)


        # 计算电路板边界
        self.eda.CalcBoardBox()
        self.board_x_min = self.eda.board.left
        self.board_x_max = self.eda.board.right
        self.board_y_min = self.eda.board.bottom
        self.board_y_max = self.eda.board.top

        # 创建元件图
        self.G = nx.Graph()

        # 初始位置使用EDA中的组件位置
        self.component_positions = {}
        self.component_sizes = {}
        for comp_name, comp_obj in self.eda.comps.items():
            self.G.add_node(comp_name)
            self.component_positions[comp_name] = (comp_obj.pos.x, comp_obj.pos.y)
            self.component_sizes[comp_name] = (comp_obj.boxSize.width, comp_obj.boxSize.height)

        # 基于网络连接添加边
        self._build_connections()

    def _build_connections(self):
        """基于网络连接建立元件之间的连接关系"""
        # 直接使用EDA框架的pinName2netName映射和comps数据
        pin_to_component = {}
        for comp_name, comp_obj in self.eda.comps.items():
            for pin_name in comp_obj.pins.keys():
                pin_to_component[pin_name] = comp_name

        # 遍历每个网络，找出连接的元件
        for net_name, net_obj in self.eda.nets.items():
            connected_components = set()
            # 直接使用网络对象的pins字典
            for pin_name in net_obj.pins.keys():
                if pin_name in pin_to_component:
                    connected_components.add(pin_to_component[pin_name])

            # 为相互连接的元件添加边
            components_list = list(connected_components)
            for i in range(len(components_list)):
                for j in range(i+1, len(components_list)):
                    comp1 = components_list[i]
                    comp2 = components_list[j]
                    # 如果边已存在，增加权重，否则创建新边
                    if self.G.has_edge(comp1, comp2):
                        self.G[comp1][comp2]['weight'] += 1
                    else:
                        self.G.add_edge(comp1, comp2, weight=1)

    def apply_force_directed_layout(self, iterations=100, k=None, scale=1):
        """应用力导向布局算法"""
        if not k:
            # 如果没有指定k，根据节点数自动计算
            k = 1.0 * np.sqrt((self.board_x_max - self.board_x_min) *
                              (self.board_y_max - self.board_y_min) / len(self.G))

        # 使用networkx的spring_layout进行布局
        pos = nx.spring_layout(
            self.G,
            k=k,
            pos=self.component_positions,
            iterations=iterations,
            weight='weight'
        )

        # 缩放回原始坐标系并确保在电路板范围内
        board_width = self.board_x_max - self.board_x_min
        board_height = self.board_y_max - self.board_y_min

        # 缩放系数
        scale_factor_x = board_width * scale
        scale_factor_y = board_height * scale

        # 应用缩放并确保在板内
        for comp_name, (x, y) in pos.items():
            width, height = self.component_sizes[comp_name]
            half_width = width / 2
            half_height = height / 2

            # 缩放到实际大小并调整到板中心
            new_x = self.board_x_min + (x + 0.5) * scale_factor_x
            new_y = self.board_y_min + (y + 0.5) * scale_factor_y

            # # 确保元件不超出边界
            # new_x = max(self.board_x_min + half_width, min(new_x, self.board_x_max - half_width))
            # new_y = max(self.board_y_min + half_height, min(new_y, self.board_y_max - half_height))

            # 更新位置
            self.component_positions[comp_name] = (new_x,  new_y)

    def resolve_overlaps(self, iterations=50):
        """解决元件重叠问题"""
        for _ in range(iterations):
            overlap_resolved = True
            # 直接使用EDA框架的comps数据
            components = list(self.eda.comps.keys())

            # 检查每对元件是否重叠
            for i in range(len(components)):
                for j in range(i+1, len(components)):
                    comp1 = components[i]
                    comp2 = components[j]

                    # 获取元件的位置和尺寸
                    x1, y1 = self.component_positions[comp1]
                    w1, h1 = self.component_sizes[comp1]

                    x2, y2 = self.component_positions[comp2]
                    w2, h2 = self.component_sizes[comp2]

                    # 检查是否重叠
                    if (abs(x1 - x2) < (w1 + w2)/2 and
                            abs(y1 - y2) < (h1 + h2)/2):

                        # 计算重叠量
                        overlap_x = (w1 + w2)/2 - abs(x1 - x2)
                        overlap_y = (h1 + h2)/2 - abs(y1 - y2)

                        # 选择沿着较小重叠方向移动
                        if overlap_x < overlap_y:
                            # 水平分离
                            if x1 < x2:
                                x1 -= overlap_x / 2
                                x2 += overlap_x / 2
                            else:
                                x1 += overlap_x / 2
                                x2 -= overlap_x / 2
                        else:
                            # 垂直分离
                            if y1 < y2:
                                y1 -= overlap_y / 2
                                y2 += overlap_y / 2
                            else:
                                y1 += overlap_y / 2
                                y2 -= overlap_y / 2

                        # 确保在板内
                        x1 = max(self.board_x_min + w1/2, min(x1, self.board_x_max - w1/2))
                        y1 = max(self.board_y_min + h1/2, min(y1, self.board_y_max - h1/2))

                        x2 = max(self.board_x_min + w2/2, min(x2, self.board_x_max - w2/2))
                        y2 = max(self.board_y_min + h2/2, min(y2, self.board_y_max - h2/2))

                        # 更新位置
                        self.component_positions[comp1] = (x1, y1)
                        self.component_positions[comp2] = (x2, y2)

                        overlap_resolved = False

            # 如果没有重叠，提前结束迭代
            if overlap_resolved:
                break

    def visualize(self, show_connections=True):
        """可视化电路板布局"""
        fig, ax = plt.subplots(figsize=(10, 8))

        # 绘制电路板边界 - 从EDA对象获取板框信息
        if self.eda.boardArea:
            # 获取主板框（outline）
            outline_area = None
            for area_id, area_obj in self.eda.boardArea.items():
                if hasattr(area_obj, 'isOutline') and area_obj.isOutline:
                    outline_area = area_obj
                    break

            if outline_area is None and self.eda.boardArea:
                # 如果没有找到outline，使用第一个板框
                outline_area = list(self.eda.boardArea.values())[0]

            if outline_area and hasattr(outline_area, 'polyline'):
                board_x = [p.x for p in outline_area.polyline]
                board_y = [p.y for p in outline_area.polyline]
                ax.plot(board_x, board_y, 'k-', linewidth=2)

        # 绘制元件
        for comp_name, (x, y) in self.component_positions.items():
            width, height = self.component_sizes[comp_name]
            rect = Rectangle((x - width/2, y - height/2), width, height,
                             fill=True, alpha=0.7, edgecolor='black', facecolor='lightblue')
            ax.add_patch(rect)
            ax.text(x, y, comp_name, ha='center', va='center', fontsize=8)

        # 绘制网络连接
        if show_connections:
            for comp1, comp2 in self.G.edges():
                x1, y1 = self.component_positions[comp1]
                x2, y2 = self.component_positions[comp2]
                edge_data = self.G[comp1][comp2]
                weight = edge_data.get('weight', 1)
                ax.plot([x1, x2], [y1, y2], 'r-', alpha=0.5, linewidth=0.5*weight)

        # 设置坐标轴
        ax.set_xlim(self.board_x_min - 100, self.board_x_max + 100)
        ax.set_ylim(self.board_y_min - 100, self.board_y_max + 100)
        ax.set_aspect('equal')
        ax.set_title('PCB ForceDirected Layout')

        plt.tight_layout()
        plt.show()

# 主程序
if __name__ == "__main__":
    layout = EDAForceLayout('Data/test.json')
    layout.eda.Show()
    # 应用力导向布局
    layout.apply_force_directed_layout(iterations=300)

    for comp_name, (x, y) in layout.component_positions.items():
        layout.eda.MoveComp(comp_name, Point(x, y))

    layout.eda.Show()

    # 解决重叠问题
    # layout.resolve_overlaps()

    # 可视化结果
    layout.visualize()