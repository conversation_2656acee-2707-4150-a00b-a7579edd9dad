import json
import logging
from collections import defaultdict

import networkx as nx
from matplotlib import pyplot as plt
from rtree import index
from BaseDefine import *


delta = 1   # 可能需要根据 mil / mm 微调

# logger = logging.getLogger("Merge")


class MergeUnclosedArea:
    def __init__(self, unclosedAreaDict):
        """
        :param unclosedAreaDict: 需要同类，要么 board，要么 keepout
        """
        self.unclosedAreasDict = unclosedAreaDict   # areaId->info

        self.segment2Ori = {}   # (start, end)->oriInfo 只保留线段，同时可以对相同点进行去重

        self.vertex_map = {}  # 防止浮点数，将点标准化
        self.G = nx.Graph()
        self.basis_cycles = None
        self.unique_cycles = []
        self.closed_polygons = []

        self.mergedArea = defaultdict(list)
        self.mergedAreaIDList = []
        self.mergedAreaIDSet = set()
        self.unMergedAreaIDList = []

        self.InitData()

    def InitData(self):
        """
        init self.segment2Ori, start 在 end 左下
        :return:
        """
        segmentDictList = defaultdict(list)
        for areaId, areaInfo in self.unclosedAreasDict.items():
            if areaInfo[POLYLINE_KEY]:
                start = (areaInfo[POLYLINE_KEY][0][0], areaInfo[POLYLINE_KEY][0][1])
                end = (areaInfo[POLYLINE_KEY][-1][0], areaInfo[POLYLINE_KEY][-1][1])
                if CheckSamePoint(start, end):
                    continue
                lines = areaInfo[POLYLINE_KEY]
                for i in range(len(lines) - 1):
                    startPoint = (lines[i][0], lines[i][1], lines[i][2])
                    endPoint = (lines[i+1][0], lines[i+1][1], lines[i+1][2])

                    start = (lines[i][0], lines[i][1])
                    end = (lines[i+1][0], lines[i+1][1])

                    if startPoint[0] < endPoint[0]:
                        segmentDictList[(start, end)].append([startPoint, endPoint, areaId])
                    elif startPoint[0] > endPoint[0]:
                        segmentDictList[(end, start)].append([(endPoint[0], endPoint[1], -startPoint[2]),
                                                              (startPoint[0], startPoint[1], 0),
                                                              areaId])
                    else:
                        # x 相同，y
                        if startPoint[1] < endPoint[1]:
                            segmentDictList[(start, end)].append([startPoint, endPoint, areaId])
                        else:
                            segmentDictList[(end, start)].append([(endPoint[0], endPoint[1], -startPoint[2]),
                                                                  (startPoint[0], startPoint[1], 0),
                                                                  areaId])

        for segment, info in segmentDictList.items():
            if len(info) <= 1:
                # 不需要去重处理
                self.segment2Ori[segment] = (info[0][0], info[0][1], [info[0][2]])
                continue
            maxArc = 0
            areaIdSet = set()   # 同一线段可能对应多个 areaId
            for segInfo in info:
                if abs(segInfo[0][2]) > abs(maxArc):
                    maxArc = segInfo[0][2]
                areaIdSet.add(segInfo[2])
            self.segment2Ori[segment] = ((segment[0][0], segment[0][1], maxArc), info[0][1], list(areaIdSet))

    def StandardizedPoints(self):
        """
        对浮点数坐标进行标准化，以便后续创建图
        :param: self.segment2Ori
        :return: self.vertex_map[point] = standardized
        """
        idx = index.Index()
        for segment, info in self.segment2Ori.items():
            for point in segment:
                x, y = point
                nearby = list(idx.nearest((x, y, x, y), num_results=1, objects=True))
                if nearby and distance(point, nearby[0].bbox[:2]) < delta:
                    standardized = (nearby[0].bbox[0], nearby[0].bbox[1])
                else:
                    standardized = (x, y)
                    idx.insert(int(info[2][0]), (x, y, x, y))
                self.vertex_map[point] = standardized

    def CalculateBasisCycles(self):
        """
        计算基环
        :param: self.segment2Ori, self.vertex_map
        :return: self.G, self.basis_cycles
        """
        for segment, info in self.segment2Ori.items():
            node1 = segment[0]
            node2 = segment[1]
            self.G.add_edge(self.vertex_map[node1], self.vertex_map[node2], areaInfo=info)
        self.basis_cycles = nx.cycle_basis(self.G)
        # nx.draw(self.G, with_labels=True)
        # plt.show()

    def ProcessBasisCycles(self):
        """
        将各基环还原为封闭多边形，
        :return:
        """
        for cycle in self.basis_cycles:
            if len(cycle) < 2:
                # 避免自环
                # logger.warning(f"Warning1: 存在自环{cycle}")
                for i in range(len(cycle)):
                    u, v = cycle[i], cycle[(i + 1) % len(cycle)]
                    if not self.G.has_edge(u, v):
                        break
                    areaId = self.G[u][v]['areaInfo']
                    self.mergedAreaIDSet.update(set(areaId[2]))
                continue
            # 1. 标准化环表示（固定起点为最小坐标的顶点）
            start_idx = min(range(len(cycle)), key=lambda i: cycle[i])
            normalized = cycle[start_idx:] + cycle[:start_idx]

            # 2. 检查是否为有效多边形（所有相邻顶点间存在边）
            is_valid = True
            points = []
            mergedAreaID = set()
            for i in range(len(normalized)):
                u, v = normalized[i], normalized[(i + 1) % len(normalized)]
                if not self.G.has_edge(u, v):
                    # logger.warning(f"Warning2: 边无效{u, v}")
                    is_valid = False
                    break

                areaInfo = self.G[u][v]['areaInfo']
                mergedAreaID.update(set(areaInfo[2]))
                self.mergedAreaIDSet.update(set(areaInfo[2]))
                curNode = (areaInfo[0][0], areaInfo[0][1])
                if self.vertex_map[curNode] == u:
                    if not points:
                        points.append(areaInfo[0])
                    else:
                        points[-1] = areaInfo[0]
                    points.append(areaInfo[1])
                else:
                    if not points:
                        points.append((areaInfo[1][0], areaInfo[1][1], -areaInfo[0][2]))    # 注意角度要变换
                    else:
                        points[-1] = (areaInfo[1][0], areaInfo[1][1], -areaInfo[0][2])
                    points.append(areaInfo[0])

            if is_valid and normalized not in self.unique_cycles:
                self.unique_cycles.append(normalized)
                self.closed_polygons.append(points)
                self.mergedArea[frozenset(mergedAreaID)].append(points)

    def CheckUnmergedArea(self):
        """
        检查是否存在未被合并的非封闭 Area
        :return:
        """
        unMerged = []
        mergedAreaIDList = list(self.mergedAreaIDSet)
        for areaId in self.unclosedAreasDict.keys():
            if areaId not in mergedAreaIDList:
                unMerged.append(areaId)
        # logger.info(f"Failed: {unMerged}")
        return unMerged

    def GetClosedPolygons(self):
        """
        对非封闭 Area 进行合并，获得封闭 Area，但可能存在未合并的
        :return:
        """
        self.StandardizedPoints()
        self.CalculateBasisCycles()
        self.ProcessBasisCycles()
        self.unMergedAreaIDList = self.CheckUnmergedArea()



def ShowPolygons(polygons, title):
    for i, polygon in enumerate(polygons):
        if i == 3:
            a = 1
        print("绘制：", i)
        x = []
        y = []
        # for j in range(len(polygon)):
        #     x = [polygon[j][0], polygon[(j + 1) % len(polygon)][0]]
        #     y = [polygon[j][1], polygon[(j + 1) % len(polygon)][1]]
        #     plt.plot(x, y, 'r-')  # 绘制红色线段
        #     plt.show(block=False)
        for point in polygon:
            x.append(point[0])
            y.append(point[1])
        plt.plot(x, y, 'r-')  # 绘制红色线段
        plt.show(block=False)
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.title(title)
    plt.grid(True)
    plt.show()


def CheckSamePoint(point1, point2):
    x = abs(point1[0] - point2[0])
    y = abs(point1[1] - point2[1])
    return x < 0.0001 and y < 0.0001


def distance(point1, point2):
    return math.dist(point1, point2)


def MergeAreaInJson(areaJson):
    """
    对 Area 进行修正，
    :param areaJson: json["Area"]
    :return: areaResultDict 修正后的Area
    """
    unclosedAreaDict = {
        BOARD_TYPE: {},
        KEEP_OUT_TYPE: {}
    }
    closedAreaDict = {}
    for areaId, areaInfoDict in areaJson.items():
        if CIRCLE_KEY in areaInfoDict.keys() or CIRCLE_KEY_ALLEGRO in areaInfoDict.keys():
            closedAreaDict[areaId] = areaInfoDict
            continue

        if POLYLINE_KEY in areaInfoDict.keys() and areaInfoDict[POLYLINE_KEY]:
            polyline = areaInfoDict[POLYLINE_KEY]
            if CheckSamePoint(polyline[0], polyline[-1]):
                # 闭合
                closedAreaDict[areaId] = areaInfoDict
            else:
                # 不闭合
                if areaInfoDict[TYPE_KEY] == BOARD_TYPE:
                    unclosedAreaDict[BOARD_TYPE][areaId] = areaInfoDict
                else:
                    unclosedAreaDict[KEEP_OUT_TYPE][areaId] = areaInfoDict

    areaResultDict = {}
    if len(unclosedAreaDict[BOARD_TYPE]) > 0:
        GetUC = MergeUnclosedArea(unclosedAreaDict[BOARD_TYPE])
        GetUC.GetClosedPolygons()

        idIndex = 0
        maxIndex = max([int(areaId) for areaId in areaJson.keys()])
        for areaId in areaJson.keys():
            # 保存未处理的 Area
            if areaId not in GetUC.mergedAreaIDSet:
                areaResultDict[areaId] = areaJson[areaId]

        for ids, pointsList in GetUC.mergedArea.items():
            for points in pointsList:
                areaId = list(ids)[0]
                flag = False
                for idx in ids:
                    if idx not in areaResultDict.keys():
                        areaId = idx
                        flag = True
                        break
                if not flag:
                    # 说明超出编号
                    areaId = maxIndex + 1
                    maxIndex += 1
                areaResultDict[areaId] = {
                    TYPE_KEY: BOARD_TYPE,
                    LAYER_KEY: "All",
                    POLYLINE_KEY: points
                }
                # logger.info(f"Success: {areaId} Merged with {ids}")
                idIndex += 1

        return areaResultDict
    else:
        return areaJson


if __name__=='__main__':
    pcbJson = "D:\\DataMag\\TempAllegro\\Unclosed\\W006C096A0_很多自环\\GMSL-R-GZ.json"

    mergedPcbJson = "D:\\DataMag\\TempAllegro\\Unclosed\\W006C096A0_很多自环\\GMSL-R-GZ_mergedT.json"
    with open(pcbJson, "r", encoding='utf-8') as file:
        dataDict = json.load(file)
        file.close()

    newAreaDict = MergeAreaInJson(dataDict[AREA_KEY])
    dataDict[AREA_KEY] = newAreaDict

    with open(mergedPcbJson, "w", encoding='utf-8') as f:
        json.dump(dataDict, f, ensure_ascii=False, indent=4)
        f.close()
    print("写入完成")
