import math

from BaseDefine import *

class TreeNode:
    def __init__(self, value, id=0):
        self.value = value
        self.left = None
        self.right = None
        self.id = id


class BinarySearchTree:
    def __init__(self):
        self.root = None
        self.zero = 1e-3

    def insert(self, value, x):
        if self.root is None:
            self.root = TreeNode(value)
        else:
            self._insert_recursive(self.root, value, x)

    def _insert_recursive(self, node, value, x):
        if abs(value.y_at_x(x) - node.value.y_at_x(x)) < self.zero:
            if value.slope() < node.value.slope():
                if node.left is None:
                    node.left = TreeNode(value)
                else:
                    self._insert_recursive(node.left, value, x)
            else:
                if node.right is None:
                    node.right = TreeNode(value)
                else:
                    self._insert_recursive(node.right, value, x)
            return
        elif value.y_at_x(x) < node.value.y_at_x(x):
            if node.left is None:
                node.left = TreeNode(value)
            else:
                self._insert_recursive(node.left, value, x)
        else:
            if node.right is None:
                node.right = TreeNode(value)
            else:
                self._insert_recursive(node.right, value, x)

    def delete(self, value, x):
        self.root = self._delete_recursive(self.root, value, x)

    def _delete_recursive(self, node, value, x):
        if node is None:
            return node

        if value.y_at_x(x) < node.value.y_at_x(x):
            node.left = self._delete_recursive(node.left, value, x)
        elif value.y_at_x(x) > node.value.y_at_x(x):
            node.right = self._delete_recursive(node.right, value, x)
        else:
            if node.left is None:
                return node.right
            elif node.right is None:
                return node.left

            temp = self._find_min(node.right)
            node.value = temp.value
            node.right = self._delete_recursive(node.right, temp.value, x)

        return node

    def _find_min(self, node):
        while node.left is not None:
            node = node.left
        return node

    def _find_max(self, node):
        while node.right is not None:
            node = node.right
        return node

    def find_neighbors(self, value, x):
        left_neighbor = None
        right_neighbor = None
        current = self.root

        while current:
            if abs(value.y_at_x(x) - current.value.y_at_x(x)) < self.zero:
                # 如果插值一样，可能是目标值
                if value == current.value:
                    if current.left:
                        left_neighbor = self._find_max(current.left)
                    if current.right:
                        right_neighbor = self._find_min(current.right)
                    break
                else:
                    if value.slope() < current.value.slope():
                        right_neighbor = current
                        current = current.left
                    else:
                        left_neighbor = current
                        current = current.right
            elif value.y_at_x(x) < current.value.y_at_x(x):
                # 如果目标值小于当前节点的值，更新右邻居并向左子树查找
                right_neighbor = current
                current = current.left
            else:
                # 如果目标值大于当前节点的值，更新左邻居并向右子树查找
                left_neighbor = current
                current = current.right

        left_value = left_neighbor.value if left_neighbor else None
        right_value = right_neighbor.value if right_neighbor else None
        return left_value, right_value

    def replace(self, value, new_value, x):
        left_neighbor = None
        right_neighbor = None
        current = self.root

        while current:
            if abs(value.y_at_x(x) - current.value.y_at_x(x)) < self.zero:
                if value == current.value:
                    # 如果找到目标值，更换为new_value
                    current.value = new_value
                    return
                else:
                    if value.slope() < current.value.slope():
                        right_neighbor = current
                        current = current.left
                    else:
                        left_neighbor = current
                        current = current.right
            elif value.y_at_x(x) < current.value.y_at_x(x):
                # 如果目标值小于当前节点的值，更新右邻居并向左子树查找
                right_neighbor = current
                current = current.left
            else:
                # 如果目标值大于当前节点的值，更新左邻居并向右子树查找
                left_neighbor = current
                current = current.right

    def showtree(self, node=None, level=0):
        if node is None:
            node = self.root
        if node.right:
            print('right')
            self.showtree(node.right, level + 1)
        print(f'{level}' + f'{node.value.start}  {node.value.end}')
        if node.left:
            print('left')
            self.showtree(node.left, level + 1)


class Event:
    def __init__(self, x, event_type, segment, other_segment, id):
        self.x = x
        self.event_type = event_type
        self.segment = segment
        self.other_segment = other_segment
        self.id = id

    def __lt__(self, other):
        if self.x != other.x:
            return self.x < other.x
        elif self.event_type != other.event_type:
            return self.event_type < other.event_type
        elif self.id != other.id:
            return self.id < other.id
        else:
            return self.segment < other.segment


class LineDetect:
    def __init__(self):
        self.zero = 1e-9

    def line_intersection(self, l1: Line, l2: Line):
        """
        计算两条线段的角度
        :param l1: 第一根线
        :param l2: 第二根线
        :return:
        """
        # 线段a-b: (ax, ay) -> (bx, by)
        ax, ay, bx, by = l1.start.x, l1.start.y, l1.end.x, l1.end.y
        # 线段c-d: (cx, cy) -> (dx, dy)
        cx, cy, dx, dy = l2.start.x, l2.start.y, l2.end.x, l2.end.y

        # 计算线段a-b的方向向量
        d_ab = (bx - ax, by - ay)
        # 计算线段c-d的方向向量
        d_cd = (dx - cx, dy - cy)

        # 计算叉积
        det = d_ab[0] * d_cd[1] - d_ab[1] * d_cd[0]

        if det == 0:
            # 两条线段平行或共线
            return None

        t = ((cx - ax) * d_cd[1] - (cy - ay) * d_cd[0]) / det
        u = ((cx - ax) * d_ab[1] - (cy - ay) * d_ab[0]) / det

        if 0 <= t <= 1 and 0 <= u <= 1:
            # 交点在两条线段上
            px = ax + t * d_ab[0]
            py = ay + t * d_ab[1]
            return Point(px, py)

        return None

    def line_intersection_optimized(self, l1: Line, l2: Line):
        ax, ay, bx, by = l1.start.x, l1.start.y, l1.end.x, l1.end.y
        cx, cy, dx, dy = l2.start.x, l2.start.y, l2.end.x, l2.end.y

        # 快速排斥实验针对扫描线优化
        # 首位相接的直线不算重叠
        if ((max(ay, by) < min(cy, dy) or min(ay, by) > max(cy, dy))
            or (l1.start == l2.start or l1.start == l2.end or l1.end == l2.start or l1.end == l2.end)):
            return None

        # # 跨立实验针对扫描线优化
        # def cross(o, a, b):
        #     return (a.x - o.x) * (b.y - o.y) - (a.y - o.y) * (b.x - o.x)
        
        # c1 = cross(l1.start, l1.end, l2.start)
        # c2 = cross(l1.start, l1.end, l2.end)
        # if (c1 < 0 and c2 < 0) or (c1 > 0 and c2 > 0):
        #     return None

        # 参数方程法计算交点
        d_ab = (bx - ax, by - ay)
        d_cd = (dx - cx, dy - cy)
        det = d_ab[0] * d_cd[1] - d_ab[1] * d_cd[0]
        if math.isclose(det, 0, abs_tol=1e-6):
            return None
        
        t = ((cx - ax) * d_cd[1] - (cy - ay) * d_cd[0]) / det
        u = ((cx - ax) * d_ab[1] - (cy - ay) * d_ab[0]) / det
        
        if 0 <= t <= 1 and 0 <= u <= 1:
            return Point(ax + t * d_ab[0], ay + t * d_ab[1])
        return None

    def count_intersections(self, segments):
        events = []
        # 生成事件点，包括线段的左端点和右端点
        for i, segment in enumerate(segments):
            events.append((segment.start.x, 0, i))  # 左端点事件
            events.append((segment.end.x, 1, i))  # 右端点事件

        # 按x坐标排序事件点
        events.sort()

        active_segments = []
        intersection_points = []

        for event in events:
            x, event_type, segment_index = event
            segment = segments[segment_index]

            if event_type == 0:  # 左端点事件
                # 检查新加入的线段与当前活跃线段是否相交
                for active_segment in active_segments:
                    # intersection_point = self.line_intersection(active_segment, segment)
                    intersection_point = self.line_intersection_optimized(active_segment, segment)
                    if intersection_point:
                        intersection_points.append(intersection_point)
                # 将新线段加入活跃线段集合
                active_segments.append(segment)
            else:  # 右端点事件
                # 移除该线段
                active_segments.remove(segment)

        return intersection_points

    def CalcAllCrossPoint(self, lines):
        """
        计算全部的交叉点
        :param lines: 飞线列表
        :return: 交点列表
        """
        crossPoints = self.count_intersections(lines)

        return crossPoints
