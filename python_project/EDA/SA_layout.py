import random
import math
from SA_utils import *
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import time
import copy
from EasyEDA import EasyEDA
from BaseDefine import *

import logging
from logging.handlers import RotatingFileHandler

class SimulatedAnnealingPlacer:
    def __init__(self, eda: EasyEDA, grid_size=(500, 500),
                 T_start=100, T_end=1, cooling=0.95, iter_per_T=100):
        self.eda = eda
        # 存储上一次保存的元件位置信息
        self.last_comps_info = {}
        # 存储目前最佳的元件位置信息
        self.best_comps_info = {}

        self.last_cost = float('inf')
        self.best_cost = float('inf')

        self.grid_size = grid_size
        self.cell_w = (eda.board.right - eda.board.left) / self.grid_size[0]
        self.cell_h = (eda.board.top - eda.board.bottom) / self.grid_size[1]
        self.T_start = T_start
        self.T_end = T_end
        self.cooling = cooling
        self.iter_per_T = iter_per_T

        # 用于第二阶段的动态调整权重
        self.metric_history = [] 
        self.iteration = 0
        # 重叠 周长 线长 交叉 距离方差 数量方差
        self.weights = np.array([0, 1.0, 0, 0, 0, 1.0])  # 第二阶段的初始权重
        self.weights = self._weight_normalize(self.weights) # 对权重进行归一化
        # self.original_terms = []
        self.adjust_factor = 0.1

        self.num_weights = 5000

        self._init_logger()  # 初始化日志配置

    def _init_logger(self):
        """初始化日志配置"""
        self.logger = logging.getLogger('EasyEDA')
        self.logger.setLevel(logging.INFO)
        
        # 创建文件处理器，每个日志文件最大5MB，保留3个备份
        file_handler = RotatingFileHandler(
            'eda_system.log', 
            maxBytes=50*1024*1024,
            backupCount=3,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
    
    def _is_within_bounds(self, eda: EasyEDA):
        for comp_name in eda.comps.keys():
            if not eda.is_in_board(comp_name):
                return False
        return True
    
    def _get_gap(self, name1, name2):
        type1 = self._get_type(name1)
        type2 = self._get_type(name2)
        if type1 ==0 and type2 == 0:
            return 0.3
        elif type1 * type2 == 0:
            return 0.3
        else:
            return 0.15
        
    def _get_type(self, name):
        # 粗略地对不同元件进行分组
        if name[0] == 'U' or name[0] == 'J':
            return 0
        if name[0] == 'C' or name[0] == 'R':
            return 1
        else:
            return 2

    def _generate_initial_layout(self):
        # 根据元件面积从大到小排序
        sorted_components = sorted(self.components.items(), key=lambda x: x[1]['w'] * x[1]['h'], reverse=True)
        layout = {}
        grid_w, grid_h = self.grid_size
        center_x, center_y = grid_w // 2, grid_h // 2

        for comp, info in sorted_components:
            placed = False
            for _ in range(1000):  # 尝试最多 1000 次放置该元件
                # 使用中心偏向的正态分布随机坐标
                x = int(np.clip(np.random.normal(center_x, grid_w / 6), 0, grid_w - 1))
                y = int(np.clip(np.random.normal(center_y, grid_h / 6), 0, grid_h - 1))
                rotation = random.choice([0, 90, 180, 270])
                layout_try = layout.copy()
                layout_try[comp] = [x, y, rotation]
                if self._is_legal_layout(layout_try):
                    layout = layout_try
                    placed = True
                    break
            if not placed:
                raise Exception(f"无法合法放置元件 {comp}，请检查尺寸或网格设置")
        self.plot_layout("Initial", layout)
        return layout
    
    def _point_to_rect_distance(self, comp: Component, main_comp: Component):
        """
        计算点 (px, py) 到矩形边界的最小轴向距离（只考虑矩形外部的情况）。
        """
        px, py = comp.pos.x, comp.pos.y
        main_box = main_comp.CalcPickBox()
        # 点在矩形左侧
        if px < main_box.left:
            dx = main_box.left - px
        # 点在矩形右侧
        elif px > main_box.right:
            dx = px - main_box.right
        else:
            dx = 0

        # 点在矩形下方
        if py < main_box.bottom:
            dy = main_box.bottom - py
        # 点在矩形上方
        elif py > main_box.top:
            dy = py - main_box.top
        else:
            dy = 0

        # 返回最小轴向距离
        return min(dx, dy) if dx or dy else 0
    
    def _closest_edge(self, comp: Component, main_comp: Component):
        # # 距离四条边的距离
        # top_dist = abs(py - y) if py < y else float('inf')             # 到上边的垂直距离
        # right_dist = abs(px - (x + w)) if px > (x + w) else float('inf')   # 到右边的水平距离
        # bottom_dist = abs(py - (y + h)) if py > (y + h) else float('inf')  # 到下边的垂直距离
        # left_dist = abs(px - x) if px < x else float('inf')           # 到左边的水平距离

        # # 形成一个列表，找到最小值对应的索引
        # distances = [top_dist, right_dist, bottom_dist, left_dist]
        # return distances.index(min(distances))
        px, py = comp.pos.x, comp.pos.y
        main_box = main_comp.CalcPickBox()
        if main_box.left < px < main_box.right:
            if py > main_box.top:
                return 0
            if py < main_box.bottom:
                return 2
        if main_box.bottom < py < main_box.top:
            if px > main_box.right:
                return 1
            if px < main_box.left:
                return 3
        return -1
    
    def _get_var(self, eda: EasyEDA):
        distance_lst = []
        edge_num_lst = [0, 0, 0, 0]
        # 计算小元件到中心器件边界距离的方差
        main_comp = eda.comps[eda.mainComp]
        for comp_name, comp in eda.comps.items():
            if comp_name == eda.mainComp:
                continue
            dis = self._point_to_rect_distance(comp, main_comp)
            distance_lst.append(dis)
            edge_idx = self._closest_edge(comp, main_comp)
            if edge_idx != -1:
                edge_num_lst[edge_idx] += 1
        return np.var(distance_lst)**0.5/np.mean(distance_lst), np.var(edge_num_lst)**0.5/np.mean(edge_num_lst)
    
    def _weight_normalize(self, weights):
        """将权重归一化，使得总和为 1"""
        total = np.sum(weights)
        if total == 0:
            raise ValueError("Sum of weights cannot be zero.")
        return weights / total
    
    def _adjust_weights(self):
        prev = np.array(self.metric_history[-2])
        curr = np.array(self.metric_history[-1])

        delta = curr - prev

        for i in range(1, len(delta)):  # 忽略 metrics[0]
            if delta[i] < 0:
                self.weights[i] *= (1 - self.adjust_factor)
            elif delta[i] > 0:
                self.weights[i] *= (1 + self.adjust_factor)

        # print(self.weights)

        # 第一次归一化
        self.weights = self._weight_normalize(self.weights)

        # # 限制范围
        # self.weights = np.clip(self.weights, 0, 0.7)

        # # 再次归一化，保证 sum=1
        # self.weights = self._weight_normalize(self.weights)
    
    def _metrics(self, eda: EasyEDA, is_sencond_stage: bool):
        overlap = eda.CalcOverlapArea()
        dis_var, num_var = self._get_var(eda)
        if not (overlap == 0 and num_var <= 0.7) and not is_sencond_stage:
            return [overlap, num_var]
        else:
            perimeter = eda.Calcperimeter()
            # wirelength = eda.CalcTotalLength()
            # cross_num = len(eda.CalcTotalCrossPoint())
            hwpl = eda.CalcHWPL()
            wirelength = hwpl
            cross_num = 0
            self.logger.info(f'{is_sencond_stage}, Overlap: {overlap:.2f}, perimeter: {perimeter:.2f}, hwpl: {wirelength:.2f}, cross_num: {cross_num}, dis_var: {dis_var:.2f}, num_var: {num_var:.2f}')
            return [overlap, perimeter, wirelength, cross_num, dis_var, num_var]
        
    def _objective(self, metrics):
        # perimeter wirelength cross_num
        if len(metrics) == 2:
            return metrics[0]
        else:
            # cost = np.sum(self.weights[1:] * np.array(metrics[1:]))
            cost = metrics[1] + metrics[2] +  self.num_weights * metrics[5]
            self.logger.info(f'cost: {cost:.2f}')
            return cost
            # return 100*metrics[1] + 25*metrics[2] + metrics[3] + 5*metrics[4] + 300*metrics[5]

    def _swap_move(self, eda: EasyEDA, is_second_stage):
        comp_names = list(eda.comps.keys())
        a, b = random.sample(comp_names, 2)
        while(a[0] == 'U' or a[0] == 'J' or b[0] == 'U' or b[0] == 'J'):
            # 大元件不参与位置互换
            a, b = random.sample(comp_names, 2)
        # 坐标先进行吸附
        comp_a_pos = Point(int(eda.comps[a].pos.x/self.cell_w)*self.cell_w, int(eda.comps[a].pos.y/self.cell_h)*self.cell_h)
        comp_b_pos = Point(int(eda.comps[b].pos.x/self.cell_w)*self.cell_w, int(eda.comps[b].pos.y/self.cell_h)*self.cell_h)
        # comp_a_pos = Point(eda.comps[a].pos.x, eda.comps[a].pos.y)
        # comp_b_pos = Point(eda.comps[b].pos.x, eda.comps[b].pos.y)
        eda.MoveComp(a, comp_b_pos)
        eda.MoveComp(b, comp_a_pos)
        if is_second_stage:
            eda.UpdateNet()

    def _tran_move(self, eda: EasyEDA, is_second_stage):
        comp_names = list(eda.comps.keys())
        comp_name = random.choice(comp_names)
        while(comp_name == eda.mainComp):
            # 中心元件不参与移动
            comp_name = random.choice(comp_names)
        dx, dy = random.choice(list(range(-9,10))), random.choice(list(range(-9,10)))
        comp = eda.comps[comp_name]
        comp_x = (int(comp.pos.x/self.cell_w) + dx) * self.cell_w
        comp_y = (int(comp.pos.y/self.cell_h) + dy) * self.cell_h
        # comp_x = comp.pos.x + dx * self.cell_w 
        # comp_y = comp.pos.y + dy * self.cell_h
        eda.MoveComp(comp_name, Point(comp_x, comp_y))
        if is_second_stage:
            eda.UpdateNet()

    def _rotate_move(self, eda: EasyEDA, is_second_stage):
        comp_names = list(eda.comps.keys())
        comp_name = random.choice(comp_names)
        while(comp_name == eda.mainComp):
            # 中心元件不参与旋转
            comp_name = random.choice(comp_names)
        rotation = random.choice([0, 90, 180, 270])
        eda.RotateComp(comp_name, rotation)
        if is_second_stage:
            eda.UpdateNet()

    def _symmetric_point(self, comp: Component, main_comp: Component):
        # 返回comp关于main_comp中心对称后的坐标
        x1, y1 = comp.pos.x, comp.pos.y
        x2, y2 = main_comp.pos.x, main_comp.pos.y
        x_sym = 2 * x2 - x1
        y_sym = 2 * y2 - y1
        return x_sym, y_sym
    
    def _sym_move(self, eda: EasyEDA, is_second_stage):
        # 小元件的坐标关于中心器件进行对称变换
        comp_names = list(eda.comps.keys())
        comp_name = random.choice(comp_names)
        while(comp_name == eda.mainComp):
            # 中心元件不参与对称
            comp_name = random.choice(comp_names)
        comp = eda.comps[comp_name]
        main_comp = eda.comps[eda.mainComp]
        x_sym, y_sym = self._symmetric_point(comp, main_comp)
        # 对称后做棋盘吸附
        x_sym = int(x_sym/self.cell_w)*self.cell_w
        y_sym = int(y_sym/self.cell_h)*self.cell_h
        eda.MoveComp(comp_name, Point(x_sym, y_sym))
        if is_second_stage:
            eda.UpdateNet()

    def _rotate_point_around_center(self, comp: Component, main_comp: Component, angle_degrees):
        ax, ay = comp.pos.x, comp.pos.y
        bx, by = main_comp.pos.x, main_comp.pos.y
        # 转为弧度
        theta = math.radians(angle_degrees)

        # 平移使B点为原点
        translated_x = ax - bx
        translated_y = ay - by

        # 旋转
        rotated_x = translated_x * math.cos(theta) - translated_y * math.sin(theta)
        rotated_y = translated_x * math.sin(theta) + translated_y * math.cos(theta)

        # 平移回原坐标
        final_x = rotated_x + bx
        final_y = rotated_y + by

        return final_x, final_y

    def _surround_move(self, eda: EasyEDA, is_second_stage):
        # 以中心器件为中心对小元件进行环绕旋转(只改变坐标)
        comp_names = list(eda.comps.keys())
        comp_name = random.choice(comp_names)
        while(comp_name == eda.mainComp):
            # 中心元件不参与对称
            comp_name = random.choice(comp_names)
        comp = eda.comps[comp_name]
        main_comp = eda.comps[eda.mainComp]
        angle = random.uniform(0, 360)

        final_x, final_y = self._rotate_point_around_center(comp, main_comp, angle)
        final_x = int(final_x/self.cell_w)*self.cell_w
        final_y = int(final_y/self.cell_h)*self.cell_h
        eda.MoveComp(comp_name, Point(final_x, final_y))
        if is_second_stage:
            eda.UpdateNet()

    def _get_eda_comps_info(self, eda: EasyEDA):
        # 将eda对象中的元件信息提取出来暂存，避免使用deepcopy
        comps_info = {}
        for comp_name, compObj in eda.comps.items():
            comps_info[comp_name] = [compObj.pos.x, compObj.pos.y, compObj.orientation]
        return comps_info
    
    def _update_eda(self, comps_info, eda: EasyEDA, is_second_stage):
        # 根据暂存的元件信息来更新eda
        for comp_name, comp_info in comps_info.items():
            [x, y, ori_new] = comp_info
            eda.MoveComp(comp_name, Point(x, y))
            eda.RotateComp(comp_name, ori_new - eda.comps[comp_name].orientation)
        if is_second_stage:
            eda.UpdateNet()
    
    def run(self, verbose=True):
        is_second_stage = False
        # 将优化分为两个阶段：(1)不满组重叠约束: 只看重叠和数量方差 (2)满足重叠约束: 考虑所有的term
        metrics = self._metrics(self.eda, is_second_stage)
        if len(metrics) == 2:
            print(f"重叠: {metrics[0]:.2f} 数量方差: {metrics[1]}")
            is_second_stage = False
        else:
            print(f"外框周长: {metrics[1]:.2f} 线长: {metrics[2]:.2f} 交叉点: {metrics[3]} 距离方差: {metrics[4]:.2f} 数量方差: {metrics[5]}")
            is_second_stage = True
            self.metric_history.append(metrics)
            # self.original_terms = metrics[1:]
        self.last_cost = self._objective(metrics)
        self.best_cost = self._objective(metrics)
        self.last_comps_info = self._get_eda_comps_info(self.eda)
        self.best_comps_info  = self._get_eda_comps_info(self.eda)

        need_goback = True

        T = self.T_start
        while T > self.T_end:
            for _ in range(self.iter_per_T):
                if need_goback:
                    self._update_eda(self.last_comps_info, self.eda, is_second_stage) # 先根据暂存的元件信息来得到上一次保存的eda状态
                rand = random.random()
                if not is_second_stage:
                    if rand < 0.3:
                        self._tran_move(self.eda, is_second_stage)
                    elif 0.3 <= rand < 0.7:
                        self._surround_move(self.eda, is_second_stage)
                    else:
                        self._sym_move(self.eda, is_second_stage)
                else:
                    if rand < 0.5:
                        self._tran_move(self.eda, is_second_stage)
                    elif 0.5 <= rand < 0.8:
                        self._swap_move(self.eda, is_second_stage)
                    elif 0.8 <= rand < 0.9:
                        self._sym_move(self.eda, is_second_stage)
                    else:
                        self._rotate_move(self.eda, is_second_stage)
                if not self._is_within_bounds(self.eda):
                    need_goback = True
                    continue
                
                metrics_new = self._metrics(self.eda, is_second_stage)
                if is_second_stage and not math.isclose(metrics_new[0], 0, rel_tol=1e-6):
                    # 在第二阶段都需要不重叠
                    need_goback = True
                    continue

                if not is_second_stage and (len(metrics_new) > 2 or T*self.cooling <= self.T_end):
                    # 开始转到第二阶段, 这一次变化无条件更新
                    is_second_stage = True
                    self._update_eda(self.best_comps_info, self.eda, is_second_stage) # 先根据暂存的元件信息来得到上一次保存的eda状态
                    metrics_new = self._metrics(self.eda, is_second_stage)
                    self.metric_history.append(metrics_new)
                    # self.original_terms = metrics_new[1:]
                    cost_new = self._objective(metrics_new)
                    # 保存这一次的元件信息到last_comps_info
                    self.last_comps_info = self._get_eda_comps_info(self.eda) 
                    self.last_cost = cost_new
                    need_goback = False
                    print("第二阶段开始！")
                    self.num_weights = math.ceil(metrics_new[1] / metrics_new[5] * 0.5)
                    print(f"重叠: {metrics_new[0]:.2f} 外框周长: {metrics_new[1]:.2f} 线长: {metrics_new[2]:.2f} 交叉点: {metrics_new[3]} 距离方差: {metrics_new[4]} 数量方差: {metrics_new[5]}")
                    self.best_comps_info = self._get_eda_comps_info(self.eda) # 保存这一次的元件信息到best_comps_info
                    self.best_cost = cost_new

                    T = self.T_start
                    continue
                    
                cost_new = self._objective(metrics_new)

                if cost_new < self.last_cost or random.random() < math.exp((self.last_cost - cost_new) / T):
                    self.last_comps_info = self._get_eda_comps_info(self.eda) # 保存这一次的元件信息到last_comps_info
                    self.last_cost = cost_new
                    need_goback = False
                    if is_second_stage:
                        self.metric_history.append(metrics_new)
                        self.iteration += 1
                        self._adjust_weights()
                    if cost_new < self.best_cost:
                        # self.eda.Show()
                        if len(metrics_new) == 2:
                            print(f"重叠: {metrics_new[0]:.2f} 数量方差: {metrics_new[1]}")
                        else:
                            print(f"重叠: {metrics_new[0]:.2f} 外框周长: {metrics_new[1]:.2f} 线长: {metrics_new[2]:.2f} 交叉点: {metrics_new[3]} 距离方差: {metrics_new[4]} 数量方差: {metrics_new[5]}")
                            
                            

                        self.best_comps_info = self._get_eda_comps_info(self.eda) # 保存这一次的元件信息到best_comps_info
                        self.best_cost = cost_new

            if verbose:
                print(f"Temp: {T:.2f}, Best cost: {self.best_cost:.2f}")
            T *= self.cooling
        self._update_eda(self.best_comps_info, self.eda, is_second_stage) # 最后根据暂存的元件信息来得到最佳的eda状态

if __name__ == "__main__":
    json_path = "samples/1.json"
    eda = EasyEDA()
    eda.InitEnv(json_path)
    eda.Show(isShowLink = True)

    placer = SimulatedAnnealingPlacer(
        eda,
        grid_size=(150, 150),
        T_start=200,
        T_end=1,
        cooling=0.95,
        iter_per_T=800
    )

    start = time.time()

    placer.run()

    end = time.time()

    print(f"最终代价：{placer.best_cost}")
    print(f"最终用时：{end - start}s")
    print(f'重叠面积: {placer.eda.CalcOverlapArea()}')
    print(f'线长: {placer.eda.CalcTotalLength()}')
    print(f'交叉点: {len(placer.eda.CalcTotalCrossPoint())}')
    print(f'外框周长: {placer.eda.Calcperimeter()}')
    print(f'半周线长: {placer.eda.CalcHWPL()}')

    placer.eda.Show(isShowLink = True)
