import math
from enum import Enum
from MSTree import manhattan_min_spanning_tree
from arc2polyline import GetPolygon

PCBNAME_KEY = 'PcbName'
COMPNAME_KEY = 'Name'
DECALNAME_KEY = 'decalName'
DECALNAME_KEY_ALLEGRO = 'DecalName'
PARTTYPE_KEY = 'PartType'
GLUED_KEY = 'IsGlued'
NETS_KEY = 'Nets'
COMPONENTS_KEY = 'Components'
POSX_KEY= 'PosX'
POSY_KEY = 'PosY'
PINS_KEY = 'Pins'
PICKBOXWIDTH_KEY = 'PickBoxWidth'
PICKBOXHEIGHT_KEY = 'PickBoxHeight'
LAYER_KEY = 'Layer'
ORIENTATION_KEY = 'Orientation'
PINPOSX_KEY = 'pinPosX'
PINPOSY_KEY = 'pinPosY'
MODULE_KEY = 'Module'
GROUP_KEY = 'Group'
MAINCOMP_KEY = 'key'
NORMALCOMP_KEY = 'normal'
MODULENAME_KEY = 'ModuleName'
AREA_KEY = 'Area'
TYPE_KEY = 'Type'
KEEP_OUT_TYPE = 'Keepout'
BOARD_TYPE = 'Board'
POLYLINE_KEY = 'Polyline'
CIRCLE_KEY = 'Circle'
CIRCLE_KEY_ALLEGRO = 'Ciecle'


class PolyType(Enum):
    Unknow = 0
    Polyline = 1
    Circle = 2

    @staticmethod
    def PolyNameToEnum(polyName):
        if 'Polyline' == polyName:
            return PolyType.Polyline
        elif 'Circle' == polyName:
            return PolyType.Circle
        else:
            return PolyType.Unknow

class LayerType(Enum):
    Unknow = 1
    Top = 2
    Bottom = 3
    All = 4

    @staticmethod
    def layerNameToEnum(layerName):
        if 'top' in layerName.lower():
            return LayerType.Top
        elif 'bottom' in layerName.lower():
            return LayerType.Bottom
        elif 'all' in layerName.lower():
            return LayerType.All
        else:
            return LayerType.Unknow

class Point:
    def __init__(self, x_, y_):
        self.x = x_
        self.y = y_

    def __str__(self):
        return f'({self.x} , {self.y})'

    def __eq__(self, other):
        if not isinstance(other, Point):
            return False
        return math.isclose(self.x, other.x, rel_tol=1e-6) and math.isclose(self.y, other.y, rel_tol=1e-6)
    
    def __lt__(self, other):
        return (self.x, self.y) < (other.x, other.y)
    
    def __hash__(self):
        return hash((self.x, self.y))

class Line:
    def __init__(self, start_, end_):
        self.start : Point = start_
        self.end : Point = end_
        self.reverse_flag = False
        if self.start > self.end:
            self.start, self.end = self.end, self.start
            self.reverse_flag = True

    def __eq__(self, other):
        if not  isinstance(other, Line):
            return False
        return self.start == other.start and self.end == other.end
    
    def __lt__(self, other):
        # 比较两条线段左端点的 y 坐标
        y1 = self.start.y
        y2 = other.start.y
        if y1 == y2:
            return self.start.x < other.start.x
        else:
            return y1 < y2

    def __hash__(self):
        return hash((self.start, self.end, self.reverse_flag))
    
    # 计算线段的斜率
    def slope(self):
        if self.end.x - self.start.x == 0:
            return float('inf')
        return (self.end.y - self.start.y) / (self.end.x - self.start.x)

    # 计算在给定x坐标处线段的y坐标
    def y_at_x(self, x):
        if self.start.x == self.end.x:
            return self.start.y
        return self.start.y + self.slope() * (x - self.start.x)

class BoxSize:
    def __init__(self, w, h):
        self.width = w
        self.height = h

    def rotate(self):
        temp = self.height
        self.height = self.width
        self.width = temp

class Box:
    def __init__(self, top_, bottom_, left_, right_):
        self.top = top_
        self.bottom = bottom_
        self.left = left_
        self.right = right_

    def Move(self, dx, dy):
        """
        移动Box
        """
        self.top += dy
        self.bottom += dy
        self.left += dx 
        self.right += dx

    def MoveTo(self, x, y):
        """
        移动Box到指定位置
        """
        dx = x - self.GetCenterPoint().x
        dy = y - self.GetCenterPoint().y
        self.Move(dx, dy)

    def Expand(self, margin):
        """
        外扩函数
        :param margin: 外扩的边距
        """
        self.top += margin
        self.bottom -= margin
        self.left -= margin
        self.right += margin

    def GetWidth(self):
        """
        获取Box的宽
        :return:
        """
        if self.right < self.left:
            print('Error: right < left')
            return 0
        
        return self.right - self.left

    def GetHeight(self):
        """
        获取Box的高
        :return:
        """
        if self.top < self.bottom:        
            print('Error: top < bottom')
            return 0
        
        return self.top - self.bottom
    
    def GetArea(self):
        """
        计算面积
        :return: 返回面积
        """
        return self.GetHeight() * self.GetWidth()
    
    def GetCenterPoint(self) -> Point:
        """
        获取中心点
        :return: 中心点坐标
        """
        return Point((self.left + self.right) / 2, (self.top + self.bottom) / 2)

    def Intersect(self, other):
        """
        判断当前Box对象与另一个Box对象所代表的矩形是否相交
        :param other: 另一个Box对象
        :return: 若相交返回True，否则返回False
        """
        if not isinstance(other, Box):
            return False
        # 判断两个矩形是否不相交
        if self.right < other.left or self.left > other.right or self.bottom > other.top or self.top < other.bottom:
            return False
        return True

    def GetIntersectionArea(self, other):
        """
        获取当前Box对象与另一个Box对象所代表的矩形的交集面积
        :param other: 另一个Box对象
        :return: 交集面积，如果不相交则返回0
        """
        if not self.Intersect(other):
            return 0
        # 计算交集矩形的边界
        intersection_left = max(self.left, other.left)
        intersection_right = min(self.right, other.right)
        intersection_bottom = max(self.bottom, other.bottom)
        intersection_top = min(self.top, other.top)
        # 计算交集矩形的宽和高
        intersection_width = intersection_right - intersection_left
        intersection_height = intersection_top - intersection_bottom
        # 计算交集面积
        return intersection_width * intersection_height

    def Contains(self, other):
        """
        判断当前Box对象是否包含另一个Box对象
        :param other: 另一个Box对象
        :return: 若包含返回True，否则返回False
        """
        if not isinstance(other, Box):
            return False
        return (self.left <= other.left and self.right >= other.right and
                self.bottom <= other.bottom and self.top >= other.top)

class Pin:
    def __init__(self, name_, pos_):
        self.pinName = name_
        self.pos : Point = pos_

class NetDot:
    def __init__(self, name_, pos_):
        self.netName = name_
        self.pos : Point = pos_

class Net:
    def __init__(self, name, pinList):
        self.netName = name
        self.pins = {}
        for pinObj in pinList:
            self.pins[pinObj.pinName] = pinObj

        self.pinPairs = []  # PinPair : <pinName, pinName>
        self.CreateNetTopology()   # 构建网络关系

    def GetAllPin(self):
        """
        获取本网络中，所有的引脚信息
        :return:
        """
        pins = []
        for pin in self.pins:
            pins.append(pin)
        return  pins

    def SetPinInfo(self, pinInfo):
        self.pins[pinInfo.pinName].pos = pinInfo.pos

    def manhattanDistance(self, pin1, pin2):
        return abs(self.pins[pin1].pos.x - self.pins[pin2].pos.x) + abs(self.pins[pin1].pos.y - self.pins[pin2].pos.y)

    def CreateNetTopology(self):
        """
        创建网络拓扑关系
        :return:
        """
        points = list(self.pins.values())
        edges = manhattan_min_spanning_tree(points)
        for edge in edges:
            self.pinPairs.append((points[edge[0]].pinName, points[edge[1]].pinName))

    def UptateNetTopology(self):
        """
        更新网络拓扑关系
        :return:
        """
        self.pinPairs.clear()
        self.CreateNetTopology()

    def GetAllLine(self):
        """
        获取所有的飞线
        :return: 飞线序列
        """
        lines = []
        for pinPair in self.pinPairs:
            line = Line(self.pins[pinPair[0]].pos, self.pins[pinPair[1]].pos)
            lines.append(line)
        return lines

    def CalcNetLength(self):
        """
        计算当前网络的线长(曼哈顿距离)
        :return: 当前拓扑结构的线长
        """
        length = 0.0
        for pinPair in self.pinPairs:
            length += (abs(self.pins[pinPair[1]].pos.x - self.pins[pinPair[0]].pos.x) + abs(self.pins[pinPair[1]].pos.y - self.pins[pinPair[0]].pos.y))
        return length
    
    def CalcNetHPWL(self):
        """
        计算当前网络的HPWL
        :return: 当前网络的HPWL
        """
        xMin = float('inf')
        xMax = float('-inf')
        yMin = float('inf')
        yMax = float('-inf')
        
        for pin in self.pins.values():
            xMin = min(xMin, pin.pos.x)
            xMax = max(xMax, pin.pos.x)
            yMin = min(yMin, pin.pos.y)
            yMax = max(yMax, pin.pos.y)

        return xMax - xMin + yMax - yMin

class Component:
    def __init__(self, json):
        self.name = None                    # 器件名称
        self.decalName = None               # 封装名称
        self.partType = None                # 器件类型
        self.pos = Point(0, 0)       # 器件中心点坐标
        self.glued = False                  # 器件是否胶粘
        self.orientation = 0.0              # 器件旋转角度
        self.layer = LayerType.Top          # 器件所在层
        self.boxSize = BoxSize(0,0)   # 器件外保卫框大小
        self.pins = {}                      # key : 引脚名; value : Pin
        self.InitData(json)

    def InitData(self, json):
        """
        从json对象中，解析出相应的数据来
        :param json: 存储器件信息的字典，从json
        :return:
        """
        self.name = json[COMPNAME_KEY]
        self.decalName = json.get(DECALNAME_KEY) or json.get(DECALNAME_KEY_ALLEGRO)
        self.partType = json[PARTTYPE_KEY]
        self.pos = Point(json[POSX_KEY], json[POSY_KEY])
        self.glued = json[GLUED_KEY]
        self.orientation = json[ORIENTATION_KEY]
        self.layer = LayerType.layerNameToEnum(json[LAYER_KEY])
        self.boxSize = BoxSize(json[PICKBOXWIDTH_KEY], json[PICKBOXHEIGHT_KEY])
        for pinName, pinPosInfo in json[PINS_KEY].items():
            pinPos = Point(pinPosInfo[PINPOSX_KEY], pinPosInfo[PINPOSY_KEY])
            pinObj = Pin(pinName, pinPos)
            self.pins[pinName] = pinObj

    def CalcPickBox(self):
        """
        计算器件的AABB包围框
        :return : Box
        """
        return  Box(self.pos.y + self.boxSize.height / 2, self.pos.y - self.boxSize.height / 2, self.pos.x - self.boxSize.width / 2, self.pos.x + self.boxSize.width / 2)

class Area:
    def __init__(self, areaId, json):
        self.areaId = areaId                    # 区域 ID
        self.closed = True                  # 区域是否闭合
        self.areaType = None               # 区域类型 Board / Keepout
        self.layer = LayerType.Top          # 区域所在层
        self.polyLineType = PolyType.Unknow     # 区分 多边形 和 圆形
        self.oriInfo = []                   # json 原始数据

        self.isOutline = False     # 区分板框和挖空区域，默认是外边界的板框

        self.polyLines = []                  # 多边形点集
        self.circle = []                    # 圆形的信息
        self.top = .0       # 用于绘图时计算绘制区域大小
        self.bottom = .0
        self.left = .0
        self.right = .0

        self.InitData(json)
        self.CalGeometry()

    def InitData(self, json):
        """
        从json对象中，解析出相应的数据来
        :param json: 存储器件信息的字典，从json
        :return:
        """
        self.areaType = json[TYPE_KEY]
        self.layer = LayerType.layerNameToEnum(json[LAYER_KEY])

        if CIRCLE_KEY_ALLEGRO in json.keys():
            # 适应 ciecle->circle
            self.polyLineType = PolyType.PolyNameToEnum(CIRCLE_KEY)
            self.oriInfo = json[CIRCLE_KEY_ALLEGRO]
        elif CIRCLE_KEY in json.keys():
            self.polyLineType = PolyType.PolyNameToEnum(CIRCLE_KEY)
            self.oriInfo = json[CIRCLE_KEY]
        elif POLYLINE_KEY in json.keys():
            # 最后判断 防止 ciecle 与 polyline 同时存在，polyline=[]
            self.polyLineType = PolyType.PolyNameToEnum(POLYLINE_KEY)
            self.oriInfo = json[POLYLINE_KEY]

    def CalGeometry(self):
        """
        计算 area 几何属性：将弧段进行切分
        :return:
        """
        if self.polyLineType == PolyType.PolyNameToEnum(POLYLINE_KEY):
            self.polyLines = self.oriInfo
            self.polyLines, self.closed = GetPolygon(self.oriInfo, numPoints=100)

            x_values = [point[0] for point in self.polyLines]
            y_values = [point[1] for point in self.polyLines]
            self.top = max(y_values)
            self.right = max(x_values)
            self.bottom = min(y_values)
            self.left = min(x_values)

        elif self.polyLineType == PolyType.PolyNameToEnum(CIRCLE_KEY):
            self.circle = self.oriInfo
            x = self.oriInfo[0]
            y = self.oriInfo[1]
            r = self.oriInfo[2]
            self.top = y + r / 2
            self.right = x + r / 2
            self.bottom = y - r / 2
            self.left = x - r / 2
            self.closed = True
