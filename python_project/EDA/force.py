import numpy as np
from EasyEDA import EasyEDA
from BaseDefine import Point
import networkx as nx
from tqdm import tqdm


class ComponentWrapper:
    """将EasyEDA组件包装为类似force.py中PCBComponent的接口"""
    def __init__(self, name, component, position):
        self.id = name
        self.width = component.boxSize.width
        self.height = component.boxSize.height
        self.position = np.array([position[0], position[1]], dtype=float)
        self.net_force = np.array([0.0, 0.0])
        self.velocity = np.array([0.0, 0.0])
        self.is_fixed = False  # 可以根据需要设置固定组件
        self.thermal_rating = 1.0  # 默认热评级

    def distance_to(self, other_component):
        """计算两个组件中心之间的距离"""
        return np.linalg.norm(self.position - other_component.position)


def force_directed_layout_pure(G, components, board, pos=None, iterations=500,
                               k_rep=1e6, k_att=0.5, damping=0.85, max_velocity=10.0,
                               temperature=1.0, cooling_rate=0.99, energy_threshold=1e-3):
    """
    完全基于force.py算法思想的力导向布局

    参数:
    - G: NetworkX图
    - components: EasyEDA组件字典
    - board: EasyEDA板子对象
    - pos: 初始位置字典
    - iterations: 最大迭代次数
    - k_rep: 斥力系数
    - k_att: 引力系数
    - damping: 阻尼系数
    - max_velocity: 最大速度限制
    - temperature: 初始温度（模拟退火）
    - cooling_rate: 冷却速率
    - energy_threshold: 收敛阈值
    """

    # 将EasyEDA组件包装为ComponentWrapper
    wrapped_components = []
    node_to_comp = {}

    for node in G.nodes():
        comp_wrapper = ComponentWrapper(node, components[node], pos[node])
        wrapped_components.append(comp_wrapper)
        node_to_comp[node] = comp_wrapper

    # PCB尺寸
    pcb_width = board.GetWidth()
    pcb_height = board.GetHeight()

    # 当前温度
    current_temperature = temperature

    # 能量历史
    energy_history = []
    prev_energy = float('inf')

    def calculate_forces():
        """完全按照force.py的calculate_forces方法实现"""
        # 重置所有力
        for comp in wrapped_components:
            comp.net_force = np.array([0.0, 0.0])

        # 1. 计算组件间的斥力(防止重叠) - 完全按照force.py实现
        for i in range(len(wrapped_components)):
            for j in range(i + 1, len(wrapped_components)):
                comp1 = wrapped_components[i]
                comp2 = wrapped_components[j]

                # 计算组件间距离
                vec = comp1.position - comp2.position
                distance = np.linalg.norm(vec)

                if distance < 1e-5:  # 避免除以零
                    distance = 1e-5
                    vec = np.array([1e-5, 0])

                # 最小安全距离(组件尺寸之和的一半) - 完全按照force.py
                min_distance = (comp1.width + comp2.width)/4 + (comp1.height + comp2.height)/4

                if distance < min_distance:
                    # 斥力 - 随距离减小而增大 - 完全按照force.py
                    force_magnitude = k_rep * (1.0 / (distance**2 + 1e-5))
                    force = force_magnitude * (vec / distance)

                    # 热力影响 - 发热元件产生更大排斥 - 完全按照force.py
                    thermal_factor = comp1.thermal_rating * comp2.thermal_rating
                    force *= thermal_factor

                    comp1.net_force += force
                    comp2.net_force -= force

        # 2. 计算连接产生的引力 - 完全按照force.py实现
        for edge in G.edges():
            comp1 = node_to_comp[edge[0]]
            comp2 = node_to_comp[edge[1]]

            # 直接使用组件中心位置，简化引脚处理
            vec = comp1.position - comp2.position
            distance = np.linalg.norm(vec)

            if distance < 1e-5:
                distance = 1e-5
                vec = np.array([1e-5, 0])

            # 理想线长 - 按照force.py的方式计算
            ideal_length = np.sqrt(np.square(comp1.height / 2) + np.square(comp1.width / 2)) + \
                           np.sqrt(np.square(comp2.height / 2) + np.square(comp2.width / 2))

            # 引力 - 胡克定律(F = k * (d - L0)) - 完全按照force.py
            force_magnitude = k_att * (distance - ideal_length) * 1.0  # weight=1.0
            force = force_magnitude * (vec / distance)

            # 应用力到组件中心 - 完全按照force.py
            comp1.net_force -= force
            comp2.net_force += force

        # 3. 计算边界约束力(防止组件移出PCB边界) - 完全按照force.py实现
        for comp in wrapped_components:
            # 左边界
            if comp.position[0] - comp.width/2 < board.left:
                comp.net_force[0] += k_rep / (abs(comp.position[0] - comp.width/2 - board.left)**2 + 1e-5)
            # 右边界
            if comp.position[0] + comp.width/2 > board.left + pcb_width:
                comp.net_force[0] -= k_rep / (abs(comp.position[0] + comp.width/2 - board.left - pcb_width)**2 + 1e-5)
            # 下边界
            if comp.position[1] - comp.height/2 < board.bottom:
                comp.net_force[1] += k_rep / (abs(comp.position[1] - comp.height/2 - board.bottom)**2 + 1e-5)
            # 上边界
            if comp.position[1] + comp.height/2 > board.bottom + pcb_height:
                comp.net_force[1] -= k_rep / (abs(comp.position[1] + comp.height/2 - board.bottom - pcb_height)**2 + 1e-5)

    def update_positions(time_step=0.1):
        """完全按照force.py的update_positions方法实现"""
        nonlocal current_temperature
        current_temperature *= cooling_rate  # 降温

        for comp in wrapped_components:
            if comp.is_fixed:
                continue  # 固定组件不移动

            # 计算加速度(F = ma, 假设质量m=1) - 完全按照force.py
            acceleration = comp.net_force

            # 更新速度(带阻尼) - 完全按照force.py
            comp.velocity = comp.velocity * damping + acceleration * time_step

            # 限制最大速度 - 完全按照force.py
            velocity_mag = np.linalg.norm(comp.velocity)
            if velocity_mag > max_velocity:
                comp.velocity = comp.velocity / velocity_mag * max_velocity

            # 添加随机扰动(模拟退火) - 完全按照force.py
            if current_temperature > 0.1:
                random_perturb = np.random.randn(2) * current_temperature
                comp.velocity += random_perturb

            # 更新位置 - 完全按照force.py
            comp.position += comp.velocity * time_step

            # 确保位置在PCB边界内 - 完全按照force.py
            comp.position[0] = np.clip(comp.position[0],
                                       board.left + comp.width/2,
                                       board.left + pcb_width - comp.width/2)
            comp.position[1] = np.clip(comp.position[1],
                                       board.bottom + comp.height/2,
                                       board.bottom + pcb_height - comp.height/2)

    def calculate_total_energy():
        """完全按照force.py的calculate_total_energy方法实现"""
        energy = 0.0

        for i in range(len(wrapped_components)):
            for j in range(i + 1, len(wrapped_components)):
                comp1 = wrapped_components[i]
                comp2 = wrapped_components[j]
                distance = comp1.distance_to(comp2)
                min_distance = (comp1.width + comp2.width)/4 + (comp1.height + comp2.height)/4
                if distance < min_distance:
                    energy += k_rep * (1.0 / (distance + 1e-5))

        for edge in G.edges():
            comp1 = node_to_comp[edge[0]]
            comp2 = node_to_comp[edge[1]]
            distance = comp1.distance_to(comp2)
            ideal_length = np.sqrt(np.square(comp1.height / 2) + np.square(comp1.width / 2)) + \
                           np.sqrt(np.square(comp2.height / 2) + np.square(comp2.width / 2))
            energy += 0.5 * k_att * (distance - ideal_length)**2 * 1.0  # weight=1.0

        return energy

    # 主优化循环 - 完全按照force.py的optimize_layout方法
    print("开始力导向布局优化...")
    for i in tqdm(range(iterations)):
        calculate_forces()
        update_positions()

        # 每10次迭代计算一次能量 - 完全按照force.py
        if i % 10 == 0:
            current_energy = calculate_total_energy()
            energy_history.append(current_energy)

            # 检查收敛 - 完全按照force.py
            if abs(current_energy - prev_energy) < energy_threshold:
                print(f"收敛于迭代 {i}, 能量: {current_energy:.4f}")
                break

            prev_energy = current_energy

            # 打印进度
            if i % 100 == 0:
                print(f"迭代 {i}, 能量: {current_energy:.4f}, 温度: {current_temperature:.3f}")

    # 转换回原始格式
    result_pos = {}
    for comp in wrapped_components:
        result_pos[comp.id] = tuple(comp.position)

    print(f"优化完成，最终能量: {energy_history[-1] if energy_history else 'N/A'}")
    return result_pos


# 保留原来的函数作为备份
def custom_spring_layout_with_real_coords(G, components, board, pos=None, iterations=500, k=None, repulsion_strength=1000.0, force_threshold=1.0, convergence_patience=10):
    """保留原函数，现在调用新的force.py风格实现"""
    return force_directed_layout_pure(G, components, board, pos, iterations,
                                      k_rep=repulsion_strength, k_att=0.5)


def advanced_force_directed_layout(G, components, board, pos=None, iterations=500, k=None,
                                   k_rep=1000.0, k_att=0.5, damping=0.85, max_velocity=10.0,
                                   temperature=1.0, cooling_rate=0.99, force_threshold=1.0,
                                   convergence_patience=10):
    """新的主函数，使用force.py的完整算法思想"""
    return force_directed_layout_pure(G, components, board, pos, iterations,
                                      k_rep, k_att, damping, max_velocity,
                                      temperature, cooling_rate, force_threshold/1000)


if __name__ == '__main__':
    eda = EasyEDA()
    eda.InitEnv('Data/single_side/0.json')

    comps = eda.comps
    nets = eda.nets
    board = eda.board
    G = nx.Graph()

    # 添加节点
    for name in comps:
        G.add_node(name)

    # 添加边（根据pinPairs）
    for net in nets.values():
        if eda.IsGNDorPowerNet(net.netName):
            continue
        for pin1, pin2 in net.pinPairs:
            comp1 = pin1.split('.')[0]
            comp2 = pin2.split('.')[0]
            if comp1 != comp2:
                G.add_edge(comp1, comp2)

    # 使用当前位置作为初始位置
    init_pos = {name: (comp.pos.x, comp.pos.y) for name, comp in comps.items()}

    # 使用force.py风格的力导向布局算法
    pos = force_directed_layout_pure(G, comps, board, pos=init_pos, iterations=1000)

    # 直接应用结果位置
    for name, (x, y) in pos.items():
        eda.MoveComp(name, Point(x, y))

    eda.Show(isShowLink=True, isShowBoard=True)
