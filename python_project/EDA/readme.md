# 一、技术手册
## 1.1 功能说明
本项目主要功能是为算法提供基础的环境，支持:  
    1）常见指标计算(如：半周线长、器件外接矩形半周长、重叠面积、飞线长度等);  
    2) 布局状态的获取;  
    3）布局状态的设置;

## 1.2 接口说明
### 1.2.1 数据结构概述
Component : 描述器件信息, 包含多个引脚  
Net : 描述网络信息，表示引脚之间的连接关系，点的集合，可以通过最小生成树的当时计算引脚的拓扑关系  
Pin : 描述引脚信息

## 1.3 使用示例
```python
    # 初始化环境
    eda = EasyEDA()
    eda.InitEnv('../Data/24.json')
    eda.Show()  # 结果可视化

    # 指标计算类接口
    print(len(eda.CalcTotalCrossPoint()))   # 计算交叉点个数
    print(eda.CalcTotalLength())            # 计算总线长

    print(eda.Calcperimeter())              # 计算器件包围框周长
    print(eda.CalcOverlapArea())            # 计算重叠面积
    print(eda.CalcHWPL())                   # 计算半周线长

    # 信息获取类接口
    print(eda.GetMainCompName())           # 获取主元件名字
    comp_obj = eda.GetComp('U18')           # 获取器件对象
    print(comp_obj.pos)
    

    # 设置类接口
    eda.MoveComp('U18',Point(0,0)) # 移动器件
    eda.RotateComp('U18', 90)     # 旋转器件
    eda.FlipComp('U18', LayerType.Bottom)  # 翻转器件
    eda.UpdateNet()    # 更新网络拓扑关系，注意这里需要手动更新
    
    eda.Show()
```