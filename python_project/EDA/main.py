from EasyEDA import *

if __name__=='__main__':
    # 原始 pcb json 数据
    # pcbJson = r'Export\Jsons\6a001aipa0_84965_16_21_28.json'
    # pcbJson = r'Export\Jsons\6a0610kla0_85719_22_51_29.json'
    # pcbJson = r'Export\Jsons\6aa2g01ka0_88872_16_02_51.json'
    # pcbJson = r'Export\Jsons\6aaar01gb7_86932_19_40_48.json'
    # pcbJson = r'Export\Jsons\6acc4007b0_88794_02_44_20.json'
    # pcbJson = r"Export\Jsons\6a001ai7a0_79573_21_19_00.json"
    pcbJson = r"Export\Jsons\sailwind_nbiot.json"

    resultFolder = "D:\\DataMag\\Temp\\plot\\A"  # 图片保存位置，图片名称格式为 pcbName+layerName

    # 初始化环境
    eda = EasyEDA()
    eda.InitEnv(pcbJson)
    eda.Show(isShowBoard = True, isShowKeepout=True)

    # 指标计算类接口
    # print(len(eda.CalcTotalCrossPoint()))   # 计算交叉点个数
    # print(eda.CalcTotalLength())            # 计算总线长

    # print(eda.Calcperimeter())              # 计算器件包围框周长
    # print(eda.CalcOverlapArea())            # 计算重叠面积
    # print(eda.CalcHWPL())                   # 计算半周线长

    # # 信息获取类接口
    # print(eda.GetMainCompName())           # 获取主元件名字
    # comp_obj = eda.GetComp('U30')           # 获取器件对象
    # print(comp_obj.pos)

    # # 设置类接口
    # eda.MoveComp('U30',Point(0,0)) # 移动器件
    # comp_obj = eda.GetComp('U30')           # 获取器件对象
    # print(comp_obj.pos)

    # eda.RotateComp('U30', 90)     # 旋转器件
    # comp_obj = eda.GetComp('U30')           # 获取器件对象
    # print(comp_obj.pos)

    # eda.FlipComp('U30', LayerType.Bottom)
    # eda.UpdateNet()

    # eda.Show()