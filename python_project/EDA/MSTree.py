import bisect

class BIT:
    def __init__(self, size):
        self.size = size
        self.mi = [float('inf')] * (size + 2)
        self.who = [-1] * (size + 2)
    
    def update(self, pos, val, idx):
        while pos <= self.size:
            if val < self.mi[pos]:
                self.mi[pos] = val
                self.who[pos] = idx
            pos += pos & -pos
    
    def query(self, pos):
        min_val = float('inf')
        min_idx = -1
        while pos > 0:
            if self.mi[pos] < min_val:
                min_val = self.mi[pos]
                min_idx = self.who[pos]
            pos -= pos & -pos
        return min_val, min_idx

class UnionFind:
    def __init__(self, size):
        self.parent = list(range(size + 1))
        self.rank = [0] * (size + 1)
    
    def find(self, x):
        if self.parent[x] != x:
            self.parent[x] = self.find(self.parent[x])
        return self.parent[x]
    
    def union(self, x, y):
        x_root = self.find(x)
        y_root = self.find(y)
        if x_root == y_root:
            return False
        if self.rank[x_root] < self.rank[y_root]:
            self.parent[x_root] = y_root
        else:
            self.parent[y_root] = x_root
            if self.rank[x_root] == self.rank[y_root]:
                self.rank[x_root] += 1
        return True

def manhattan_min_spanning_tree(points):
    # 给每个点分配唯一ID (从1开始)
    nodes = [(point.pos.x, point.pos.y, i + 1) for i, point in enumerate(points)]
    edges = []
    
    # 四次坐标变换
    for k in range(4):
        transformed = []
        for x, y, idx in nodes:
            if k == 0:   # 原始坐标
                x_prime, y_prime = x, y
            elif k == 1: # 交换坐标
                x_prime, y_prime = y, x
            elif k == 2: # x取反
                x_prime, y_prime = -x, y
            else:        # 交换并取反
                x_prime, y_prime = y, -x
            transformed.append((x_prime, y_prime, idx))
        
        # 按x'降序，y'降序排序
        transformed.sort(key=lambda p: (-p[0], -p[1]))
        
        # 离散化s = x' - y'
        s_values = [p[0] - p[1] for p in transformed]
        unique_s = sorted(set(s_values))
        m = len(unique_s)
        if m == 0:
            continue
        
        # 初始化树状数组
        bit = BIT(m)
        
        # 处理每个点
        for p in transformed:
            x_prime, y_prime, idx = p
            s = x_prime - y_prime
            # 查找离散化位置 (从1开始)
            pos = bisect.bisect_left(unique_s, s) + 1
            # 查询前缀最小值
            min_val, min_id = bit.query(pos)
            if min_id != -1:
                # 计算原始坐标的曼哈顿距离
                original_u = nodes[idx-1]  # 当前点的原始坐标
                original_v = nodes[min_id-1]  # 找到的点的原始坐标
                w = abs(original_u[0] - original_v[0]) + abs(original_u[1] - original_v[1])
                edges.append((w, idx, min_id))
            # 更新树状数组
            current_val = x_prime + y_prime
            bit.update(pos, current_val, idx)
    
    # 去重边并按权重排序
    unique_edges = {}
    for w, u, v in edges:
        if u > v:
            u, v = v, u
        if (u, v) not in unique_edges or w < unique_edges[(u, v)]:
            unique_edges[(u, v)] = w
    edges = [(w, u, v) for (u, v), w in unique_edges.items()]
    edges.sort()
    
    # Kruskal算法
    uf = UnionFind(len(nodes))
    selected_edges = []
    
    for w, u, v in edges:
        if uf.find(u) != uf.find(v):
            uf.union(u, v)
            # 转换为0-based索引并记录边
            selected_edges.append((u-1, v-1, w))
    
    return selected_edges
