import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches

import numpy as np

import ExportJson.convert
from EasyEDA import EasyEDA
from BaseDefine import Point
import networkx as nx
from collections import deque



class ComponentWrapper:
    """将EasyEDA组件包装为类似force.py中PCBComponent的接口"""
    def __init__(self, name, fixed, width,height,position):
        self.id = name
        self.is_fixed = fixed
        self.width = width
        self.height = height
        self.position = np.array([position[0], position[1]], dtype=float)
        self.net_force = np.array([0.0, 0.0])
        self.velocity = np.array([0.0, 0.0])
        self.thermal_rating = 1.0  # 默认热评级

    def distance_to(self, other_component):
        return np.linalg.norm(self.position - other_component.position)

def is_point_in_polygon(point, polygon):
    """射线法判断点是否在多边形内部"""
    x, y = point
    n = len(polygon)
    inside = False

    p1x, p1y = polygon[0]
    for i in range(n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        x_intersect = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= x_intersect:
                        inside = not inside
        p1x, p1y = p2x, p2y

    return inside

def distance_to_segment(point, segment):
    """计算点到线段的最短距离和最近点"""
    p, q = segment
    px, py = p
    qx, qy = q

    # 向量PQ
    dx = qx - px
    dy = qy - py

    # 如果线段是一个点
    if dx == 0 and dy == 0:
        return np.linalg.norm([px - point[0], py - point[1]]), (px, py)

    # 点在线段上的投影参数t
    t = ((point[0] - px) * dx + (point[1] - py) * dy) / (dx * dx + dy * dy)

    if t < 0:
        # 最近点是P
        closest_point = (px, py)
    elif t > 1:
        # 最近点是Q
        closest_point = (qx, qy)
    else:
        # 最近点在线段PQ上
        closest_point = (px + t * dx, py + t * dy)

    # 计算距离
    dist = np.linalg.norm([point[0] - closest_point[0], point[1] - closest_point[1]])
    return dist, closest_point

def distance_to_polygon(point, polygon):
    if not polygon:
        raise ValueError("多边形不能为空")

    min_dist = float('inf')
    closest_point = None

    n = len(polygon)
    for i in range(n):
        segment = (polygon[i], polygon[(i + 1) % n])
        dist, point_on_segment = distance_to_segment(point, segment)

        if dist < min_dist:
            min_dist = dist
            closest_point = point_on_segment

    # 计算方向向量 (从最近点指向原始点)
    direction = np.array([point[0] - closest_point[0], point[1] - closest_point[1]])

    # 归一化方向向量
    direction_norm = np.linalg.norm(direction)
    if direction_norm > 0:
        direction = direction / direction_norm
    else:
        # 处理极少数情况：方向向量为零
        direction = np.array([0.0, 0.0])

    return min_dist, closest_point, direction

def calculate_polygon_centroid(polygon):
    """计算多边形的中心点"""
    n = len(polygon)
    area = 0.0
    cx = 0.0
    cy = 0.0

    for i in range(n):
        j = (i + 1) % n
        cross = polygon[i][0] * polygon[j][1] - polygon[j][0] * polygon[i][1]
        area += cross
        cx += (polygon[i][0] + polygon[j][0]) * cross
        cy += (polygon[i][1] + polygon[j][1]) * cross

    area *= 0.5
    if abs(area) < 1e-10:  # 避免除零
        # 如果面积为0，返回几何中心
        cx = sum(p[0] for p in polygon) / n
        cy = sum(p[1] for p in polygon) / n
    else:
        cx /= 6.0 * area
        cy /= 6.0 * area

    return (cx, cy)

def polygons_intersect(polygon1, polygon2):
    """
    判断两个多边形是否相交

    参数:
    - polygon1: 第一个多边形的顶点列表 [(x1,y1), (x2,y2), ...]
    - polygon2: 第二个多边形的顶点列表

    返回:
    - bool: 如果两个多边形相交返回True，否则返回False
    """
    # 检查一个多边形的点是否在另一个多边形内
    for point in polygon1:
        if is_point_in_polygon(point, polygon2):
            return True

    for point in polygon2:
        if is_point_in_polygon(point, polygon1):
            return True

    # 检查边是否相交
    n1, n2 = len(polygon1), len(polygon2)
    for i in range(n1):
        edge1 = (polygon1[i], polygon1[(i + 1) % n1])
        for j in range(n2):
            edge2 = (polygon2[j], polygon2[(j + 1) % n2])
            if segments_intersect(edge1, edge2):
                return True

    return False

def segments_intersect(segment1, segment2):
    """判断两条线段是否相交"""
    p1, p2 = segment1
    p3, p4 = segment2

    # 通过叉积计算方向
    def direction(p, q, r):
        return (r[0] - p[0]) * (q[1] - p[1]) - (q[0] - p[0]) * (r[1] - p[1])

    # 检查点是否在线段上
    def on_segment(p, q, r):
        return (max(p[0], r[0]) >= q[0] >= min(p[0], r[0]) and
                max(p[1], r[1]) >= q[1] >= min(p[1], r[1]))

    d1 = direction(p3, p4, p1)
    d2 = direction(p3, p4, p2)
    d3 = direction(p1, p2, p3)
    d4 = direction(p1, p2, p4)

    # 如果线段互相穿过
    if ((d1 > 0 and d2 < 0) or (d1 < 0 and d2 > 0)) and ((d3 > 0 and d4 < 0) or (d3 < 0 and d4 > 0)):
        return True

    # 处理共线情况
    if d1 == 0 and on_segment(p3, p1, p4): return True
    if d2 == 0 and on_segment(p3, p2, p4): return True
    if d3 == 0 and on_segment(p1, p3, p2): return True
    if d4 == 0 and on_segment(p1, p4, p2): return True

    return False

def min_distance_to_polygon(polygon1, polygon2):
    """
    计算两个多边形之间的最小距离

    参数:
    - polygon1: 第一个多边形的顶点列表 [(x1,y1), (x2,y2), ...]
    - polygon2: 第二个多边形的顶点列表

    返回:
    - float: 两个多边形间的最小距离，如果相交则返回0
    """
    # 如果多边形相交，距离为0
    if polygons_intersect(polygon1, polygon2):
        return 0.0

    min_dist = float('inf')

    # 计算polygon1的每个点到polygon2的最小距离
    for point in polygon1:
        dist, _, _ = distance_to_polygon(point, polygon2)
        min_dist = min(min_dist, dist)

    # 计算polygon2的每个点到polygon1的最小距离
    for point in polygon2:
        dist, _, _ = distance_to_polygon(point, polygon1)
        min_dist = min(min_dist, dist)

    return min_dist


def create_wrapped_components(G, components, pos):
    """
    创建组件包装器列表

    参数:
    - G: NetworkX图
    - components: EasyEDA组件字典
    - pos: 初始位置字典

    返回:
    - wrapped_components: 包装后的组件列表
    - node_to_comp: 节点到组件的映射字典
    """
    wrapped_components = []
    node_to_comp = {}

    for node in G.nodes():
        comp_wrapper = ComponentWrapper(node, components[node].glued, components[node].boxSize.width, components[node].boxSize.height, pos[node])
        wrapped_components.append(comp_wrapper)
        node_to_comp[node] = comp_wrapper

    return wrapped_components, node_to_comp


def force_directed_layout(G, wrapped_components, node_to_comp, board_polygon, keepout_polygons=None, iterations=500,
                          k_rep=5e6, k_att=0.5, damping=0.85, max_velocity=10.0,
                          temperature=1.0, cooling_rate=0.99, energy_threshold=1e-3):
    """
    参数:
    - G: NetworkX图
    - wrapped_components: 包装后的组件列表
    - node_to_comp: 节点到组件的映射字典
    - board_polygon: 板框多边形点列表 [(x1,y1), (x2,y2), ...]
    - keepout_polygons: keepout区域多边形列表 [[(x1,y1), (x2,y2), ...], ...]
    - iterations: 最大迭代次数
    - k_rep: 斥力系数
    - k_att: 引力系数
    - damping: 阻尼系数
    - max_velocity: 最大速度限制
    - temperature: 初始温度（模拟退火）
    - cooling_rate: 冷却速率
    - energy_threshold: 收敛阈值
    """

    # 当前温度
    current_temperature = temperature

    # 能量历史
    energy_history = []
    prev_energy = float('inf')

    # 保存初始相对位置关系
    initial_relative_positions = {}
    component_center = np.mean([comp.position for comp in wrapped_components], axis=0)

    for comp in wrapped_components:
        # 计算每个组件相对于中心的向量
        initial_relative_positions[comp.id] = comp.position - component_center

    def calculate_forces():
        # 重置所有力
        for comp in wrapped_components:
            comp.net_force = np.array([0.0, 0.0])

        # 组件间的相互作用力
        for i in range(len(wrapped_components)):
            for j in range(i + 1, len(wrapped_components)):
                comp1 = wrapped_components[i]
                comp2 = wrapped_components[j]

                # 计算组件间距离
                vec = comp1.position - comp2.position
                distance = np.linalg.norm(vec)

                if distance < 1e-5:  # 避免除以零
                    distance = 1e-5
                    vec = np.array([1e-5, 1e-5])  # 随机方向，避免零向量

                # 计算两个矩形实际的重叠情况
                dx = abs(comp1.position[0] - comp2.position[0])
                dy = abs(comp1.position[1] - comp2.position[1])

                # 水平和垂直方向上的重叠量
                overlap_x = (comp1.width + comp2.width)/2 - dx
                overlap_y = (comp1.height + comp2.height)/2 - dy

                # 检测是否重叠
                if overlap_x > 0 and overlap_y > 0:  # 有重叠
                    # 重叠面积越大，斥力越大
                    overlap_area = overlap_x * overlap_y
                    # 使用更强的非线性斥力模型
                    force_magnitude = k_rep * (5.0 + overlap_area / (comp1.width * comp1.height))

                    # 确保力的方向是将组件分开的方向
                    if abs(vec[0]) < 1e-5 and abs(vec[1]) < 1e-5:
                        # 完全重叠的情况，给一个随机方向
                        angle = np.random.random() * 2 * np.pi
                        vec = np.array([np.cos(angle), np.sin(angle)])
                    else:
                        vec = vec / distance  # 单位向量
                    force = force_magnitude * vec

                    comp1.net_force += force
                    comp2.net_force -= force
                else:
                    # 最小安全距离(组件尺寸之和的一半加上额外间距)
                    min_distance = (comp1.width + comp2.width)/2 + (comp1.height + comp2.height)/2 + 2.0
                    if distance < min_distance:
                        # 使用更平滑的距离函数
                        force_magnitude = k_rep * (1.0 / (distance**2 + 1e-5)) * ((min_distance / distance) - 0.5)
                        force = force_magnitude * (vec / distance)
                        comp1.net_force += force
                        comp2.net_force -= force

        for edge in G.edges():
            comp1 = node_to_comp[edge[0]]
            comp2 = node_to_comp[edge[1]]
            vec = comp1.position - comp2.position
            distance = np.linalg.norm(vec)
            if distance < 1e-5:
                distance = 1e-5
                vec = np.array([1e-5, 0])
            ideal_length = np.sqrt(np.square(comp1.height / 2) + np.square(comp1.width / 2)) + \
                           np.sqrt(np.square(comp2.height / 2) + np.square(comp2.width / 2))
            # 引力 - 胡克定律(F = -k * (d - L0))
            force_magnitude = -k_att * (distance - ideal_length) * 1.0  # weight=1.0
            force = force_magnitude * (vec / distance)
            comp1.net_force += force
            comp2.net_force -= force

        # 板框边界约束
        for comp in wrapped_components:
            corners = [
                (comp.position[0] - comp.width/2, comp.position[1] - comp.height/2),  # 左下
                (comp.position[0] + comp.width/2, comp.position[1] - comp.height/2),  # 右下
                (comp.position[0] + comp.width/2, comp.position[1] + comp.height/2),  # 右上
                (comp.position[0] - comp.width/2, comp.position[1] + comp.height/2),  # 左上
            ]
            corners_inside = [is_point_in_polygon(corner, board_polygon) for corner in corners]

            # 如果组件完全在板框外
            if not any(corners_inside):
                # 计算到板框的最短距离和方向
                comp_center_dist, closest_point, direction = distance_to_polygon(comp.position, board_polygon)
                # 施加更强的力
                force_magnitude = k_rep * 5.0 * (1.0 + comp_center_dist / 100)
                force = force_magnitude * (-direction)  # 方向向板框内部
                comp.net_force += force
            # 如果组件部分在板框外
            elif not all(corners_inside):
                for i, corner in enumerate(corners):
                    if not corners_inside[i]:
                        dist, closest_point, direction = distance_to_polygon(corner, board_polygon)
                        force_magnitude = k_rep * 2.0 / (dist + 1e-5)
                        force = -force_magnitude * direction
                        comp.net_force += force
            # 组件完全在板框内，但接近边界
            else:
                center_dist, _, direction = distance_to_polygon(comp.position, board_polygon)
                boundary_margin = min(comp.width, comp.height) * 0.5
                if center_dist < boundary_margin:
                    force_magnitude = k_rep * 0.1 * (1.0 - center_dist / boundary_margin)
                    force = force_magnitude * direction
                    comp.net_force += force
        # # 计算板框中心
        # board_center = calculate_polygon_centroid(board_polygon)
        #
        # # 检查是否大部分组件都在板框外
        # components_outside = sum(1 for comp in wrapped_components
        #                          if not all(is_point_in_polygon(corner, board_polygon)
        #                                     for corner in [(comp.position[0] - comp.width/2, comp.position[1] - comp.height/2),
        #                                                    (comp.position[0] + comp.width/2, comp.position[1] - comp.height/2),
        #                                                    (comp.position[0] + comp.width/2, comp.position[1] + comp.height/2),
        #                                                    (comp.position[0] - comp.width/2, comp.position[1] + comp.height/2)]))
        #
        # # 如果大部分组件在板框外，添加强大的中心吸引力
        # if components_outside > len(wrapped_components) * 0.5:
        #     for comp in wrapped_components:
        #         if not comp.is_fixed:
        #             to_center = np.array([board_center[0] - comp.position[0],
        #                                   board_center[1] - comp.position[1]])
        #             dist = np.linalg.norm(to_center) + 1e-5
        #             direction = to_center / dist
        #             force_magnitude = k_rep * 3.0 * (1.0 + dist / 100)
        #             comp.net_force += force_magnitude * direction

        # 添加保持相对位置的约束力
        if True:
            # 计算当前布局中心点
            current_center = np.mean([comp.position for comp in wrapped_components], axis=0)

            for comp in wrapped_components:
                if not comp.is_fixed:
                    # 期望位置 = 当前中心 + 初始相对向量
                    expected_position = current_center + initial_relative_positions[comp.id]

                    # 计算约束力
                    position_diff = expected_position - comp.position
                    topology_force = position_diff * 5.0

                    comp.net_force += topology_force
        # 禁布区约束处理
        if keepout_polygons:
            for comp in wrapped_components:
                # 创建组件的边界框多边形
                comp_polygon = [
                    (comp.position[0] - comp.width/2, comp.position[1] - comp.height/2),  # 左下
                    (comp.position[0] + comp.width/2, comp.position[1] - comp.height/2),  # 右下
                    (comp.position[0] + comp.width/2, comp.position[1] + comp.height/2),  # 右上
                    (comp.position[0] - comp.width/2, comp.position[1] + comp.height/2),  # 左上
                ]

                for keepout_polygon in keepout_polygons:
                    if polygons_intersect(comp_polygon, keepout_polygon):
                        min_dist, closest_point, direction = distance_to_polygon(comp.position, keepout_polygon)
                        if is_point_in_polygon(comp.position, keepout_polygon):
                            direction = -direction
                            force_magnitude = k_rep * 5.0
                        else:
                            force_magnitude = k_rep * 2.0 / (min_dist + 1e-5)
                        force = force_magnitude * direction
                        comp.net_force += force

                    else:
                        min_dist = min_distance_to_polygon(comp_polygon, keepout_polygon)
                        safety_margin = max(comp.width, comp.height) * 0.3  # 安全距离
                        if min_dist < safety_margin:
                            center_dist, _, direction = distance_to_polygon(comp.position, keepout_polygon)
                            force_magnitude = k_rep * 0.5 * (1.0 - min_dist / safety_margin)
                            force = force_magnitude * direction
                            comp.net_force += force


    def update_positions(time_step=0.1):
        nonlocal current_temperature
        # 更缓慢的冷却策略
        if i < iterations * 0.1:
            # 初期保持高温，充分探索
            current_temperature *= 0.999
        elif i < iterations * 0.5:
            current_temperature *= 0.997  # 中期缓慢降温
        else:
            current_temperature *= cooling_rate  # 后期正常降温

        for comp in wrapped_components:
            if comp.is_fixed:
                continue
            # 增加温度对加速度的缩放影响
            acceleration = comp.net_force * min(1.0, current_temperature)
            # 随机扰动，帮助跳出局部最优
            if current_temperature > 0.3:  # 只在温度较高时添加随机扰动
                random_force = np.random.normal(0, current_temperature * 0.5, 2)
                acceleration += random_force
            # 增大全局阻尼，降低速度幅度波动
            comp.velocity = comp.velocity * damping + acceleration * time_step
            velocity_mag = np.linalg.norm(comp.velocity)
            if velocity_mag > max_velocity:
                comp.velocity = comp.velocity / velocity_mag * max_velocity
            comp.position += comp.velocity * time_step


    def calculate_total_energy():
        energy = 0.0
        for i in range(len(wrapped_components)):
            for j in range(i + 1, len(wrapped_components)):
                comp1 = wrapped_components[i]
                comp2 = wrapped_components[j]
                distance = comp1.distance_to(comp2)
                min_distance = (comp1.width + comp2.width)/2 + (comp1.height + comp2.height)/2
                if distance < min_distance:
                    energy += k_rep * (1.0 / (distance + 1e-5))

        for edge in G.edges():
            comp1 = node_to_comp[edge[0]]
            comp2 = node_to_comp[edge[1]]
            distance = comp1.distance_to(comp2)
            ideal_length = np.sqrt(np.square(comp1.height / 2) + np.square(comp1.width / 2)) + \
                           np.sqrt(np.square(comp2.height / 2) + np.square(comp2.width / 2))
            energy += 0.5 * k_att * (distance - ideal_length)**2 * 1.0  # weight=1.0

            # 3. 板框边界约束能量
        for comp in wrapped_components:
            corners = [
                (comp.position[0] - comp.width/2, comp.position[1] - comp.height/2),
                (comp.position[0] + comp.width/2, comp.position[1] - comp.height/2),
                (comp.position[0] + comp.width/2, comp.position[1] + comp.height/2),
                (comp.position[0] - comp.width/2, comp.position[1] + comp.height/2),
            ]
            corners_outside = sum(1 for corner in corners if not is_point_in_polygon(corner, board_polygon))
            if corners_outside > 0:
                energy += k_rep * corners_outside * 10.0  # 边界违规惩罚

        # 4. 禁布区约束能量
        if keepout_polygons:
            for comp in wrapped_components:
                comp_polygon = [
                    (comp.position[0] - comp.width/2, comp.position[1] - comp.height/2),
                    (comp.position[0] + comp.width/2, comp.position[1] - comp.height/2),
                    (comp.position[0] + comp.width/2, comp.position[1] + comp.height/2),
                    (comp.position[0] - comp.width/2, comp.position[1] + comp.height/2),
                ]
                for keepout_polygon in keepout_polygons:
                    if polygons_intersect(comp_polygon, keepout_polygon):
                        energy += k_rep * 5.0  # 禁布区违规惩罚

        return energy

    for i in range(iterations):
        calculate_forces()
        update_positions(0.1)

        if i % 10 == 0:
            current_energy = calculate_total_energy()
            energy_history.append(current_energy)

            if abs(current_energy - prev_energy) < energy_threshold:
                print(f"Converged at iteration {i}, Energy: {current_energy:.4f}")
                break

            prev_energy = current_energy

            if i % 100 == 0:
                print(f"Iteration: {i}, Energy: {current_energy:.4f}, Temperature: {current_temperature:.3f}")

    result_pos = {}
    for comp in wrapped_components:
        result_pos[comp.id] = tuple(comp.position)

    print(f"Optimization complete, final energy: {energy_history[-1] if energy_history else 'N/A'}")
    return result_pos


def build_topology_from_main_component(eda):
    G = nx.Graph()
    comps = eda.comps
    nets = eda.nets

    # 添加所有组件作为节点
    for name in comps:
        G.add_node(name)

    # 获取主器件
    main_comp_name = eda.GetMainCompName()
    if not main_comp_name:
        # 如果没有主器件，取第一个器件作为主器件
        main_comp_name = list(comps.keys())[0]
    else:
        print(f"Main component: {main_comp_name}")

    # 用于记录已访问的器件和网络
    visited_components = set()
    visited_nets = set()
    component_queue = deque([main_comp_name])
    visited_components.add(main_comp_name)

    while component_queue:
        current_comp = component_queue.popleft()
        current_comp_obj = comps[current_comp]
        for pin_name in current_comp_obj.pins:
            net_name = eda.pinName2netName.get(pin_name)
            if not net_name:
                continue

            if net_name in visited_nets:
                continue
            # if eda.IsGNDorPowerNet(net_name):
            #     continue
            visited_nets.add(net_name)
            net = nets[net_name]
            connected_components = set()
            for pin in net.pins:
                comp_name = pin.split('.')[0]
                if comp_name in comps:
                    connected_components.add(comp_name)
            connected_components.discard(current_comp)

            for connected_comp in connected_components:
                if connected_comp not in visited_components:
                    G.add_edge(current_comp, connected_comp)
                    # 将新发现的器件加入队列
                    component_queue.append(connected_comp)
                    visited_components.add(connected_comp)
    return G


def multiple_runs_analysis(all_positions, comps):
    """
    分析多次运行结果的位置差异和稳定性

    Args:
        all_positions: 所有运行结果的位置列表
        comps: 组件字典

    Returns:
        dict: 包含分析结果的字典
    """
    num_runs = len(all_positions)
    if num_runs < 2:
        print("需要至少2次运行才能进行比较分析")
        return {}

    print(f"\n=== Position displacement analysis for {num_runs} runs ===")

    # Calculate position displacements between all runs
    total_displacement_matrix = []
    max_displacement_overall = 0.0
    max_displacement_pair = (0, 0)
    max_displacement_comp_overall = ""

    # Compare each pair of runs
    for i in range(num_runs):
        for j in range(i + 1, num_runs):
            print(f"\nComparing run {i+1} and run {j+1}:")
            total_displacement = 0.0
            max_displacement = 0.0
            max_displacement_comp = ""

            for comp_name in comps.keys():
                x1, y1 = all_positions[i][comp_name]
                x2, y2 = all_positions[j][comp_name]

                # Calculate position displacement
                dx = x2 - x1
                dy = y2 - y1
                displacement = np.sqrt(dx*dx + dy*dy)

                total_displacement += displacement
                if displacement > max_displacement:
                    max_displacement = displacement
                    max_displacement_comp = comp_name

                if displacement > max_displacement_overall:
                    max_displacement_overall = displacement
                    max_displacement_pair = (i+1, j+1)
                    max_displacement_comp_overall = comp_name

                print(f"  {comp_name}: Δx={dx:.3f}, Δy={dy:.3f}, distance={displacement:.3f}")

            avg_displacement = total_displacement / len(comps)
            total_displacement_matrix.append({
                'pair': (i+1, j+1),
                'avg_displacement': avg_displacement,
                'max_displacement': max_displacement,
                'max_displacement_comp': max_displacement_comp,
                'total_displacement': total_displacement
            })

            print(f"  Average displacement: {avg_displacement:.3f}")
            print(f"  Maximum displacement: {max_displacement:.3f} (component: {max_displacement_comp})")
            print(f"  Total displacement: {total_displacement:.3f}")

    # Summary statistics
    print(f"\n=== Overall Statistics ===")
    avg_displacements = [result['avg_displacement'] for result in total_displacement_matrix]
    max_displacements = [result['max_displacement'] for result in total_displacement_matrix]

    print(f"Average displacement across all comparisons:")
    print(f"  Minimum: {min(avg_displacements):.3f}")
    print(f"  Maximum: {max(avg_displacements):.3f}")
    print(f"  Mean: {np.mean(avg_displacements):.3f}")
    print(f"  Standard deviation: {np.std(avg_displacements):.3f}")

    print(f"\nMaximum displacement across all comparisons:")
    print(f"  Minimum: {min(max_displacements):.3f}")
    print(f"  Maximum: {max(max_displacements):.3f}")
    print(f"  Mean: {np.mean(max_displacements):.3f}")
    print(f"  Standard deviation: {np.std(max_displacements):.3f}")

    print(f"\nGlobal maximum displacement: {max_displacement_overall:.3f}")
    print(f"Occurred between run {max_displacement_pair[0]} and run {max_displacement_pair[1]}")
    print(f"Component: {max_displacement_comp_overall}")

    # Calculate stability for each component across all runs
    print(f"\n=== Component Stability Analysis ===")
    component_stability = {}
    for comp_name in comps.keys():
        positions = [all_positions[i][comp_name] for i in range(num_runs)]
        x_positions = [pos[0] for pos in positions]
        y_positions = [pos[1] for pos in positions]

        x_std = np.std(x_positions)
        y_std = np.std(y_positions)
        position_std = np.sqrt(x_std**2 + y_std**2)

        component_stability[comp_name] = {
            'x_std': x_std,
            'y_std': y_std,
            'position_std': position_std
        }

        print(f"{comp_name}: X_std={x_std:.3f}, Y_std={y_std:.3f}, Position_std={position_std:.3f}")

    return {
        'displacement_matrix': total_displacement_matrix,
        'overall_stats': {
            'max_displacement_overall': max_displacement_overall,
            'max_displacement_pair': max_displacement_pair,
            'max_displacement_comp_overall': max_displacement_comp_overall,
            'avg_displacements_stats': {
                'min': min(avg_displacements),
                'max': max(avg_displacements),
                'mean': np.mean(avg_displacements),
                'std': np.std(avg_displacements)
            },
            'max_displacements_stats': {
                'min': min(max_displacements),
                'max': max(max_displacements),
                'mean': np.mean(max_displacements),
                'std': np.std(max_displacements)
            }
        },
        'component_stability': component_stability
    }


def BuildGraphFromEDA(eda):
    comps = eda.comps
    nets = eda.nets
    boardPolygon = None
    keepOutPolygons = []
    for boardId, board in eda.boardArea.items():
        boardPolygon = board.polyLines
        break

    for keepoutId, keepout in eda.keepoutArea.items():
        keepOutPolygons.append(keepout.polyLines)
        print("keep out:", keepoutId, keepout.polyLines)

    G = build_topology_from_main_component(eda)
    component_connections = {}
    for comp_name in comps.keys():
        connections = list(G.neighbors(comp_name))
        component_connections[comp_name] = len(connections)
    init_pos = {name: (comp.pos.x, comp.pos.y) for name, comp in comps.items()}
    components, node_to_comp = create_wrapped_components(G, comps, init_pos.copy())

    return G,components,node_to_comp,boardPolygon,keepOutPolygons


def EdaMain():
    eda = EasyEDA()
    eda.InitEnv(r'D:\Workspace\ForceDirectedNx\EDA\Data\KK_AY5.134.4508_20230928z2.json')
    eda.Show(isShowLink=False, isShowBoard=True,isShowKeepout=True)

    num_runs = 1
    all_positions = []
    for run_idx in range(num_runs):
        G,wrapped_components,node_to_comp,boardPolygon,keepOutPolygons = BuildGraphFromEDA(eda)
        pos = force_directed_layout(G, wrapped_components, node_to_comp, boardPolygon, keepout_polygons=keepOutPolygons, iterations=8000)
        all_positions.append(pos)

    for name, (x, y) in all_positions[0].items():
        eda.MoveComp(name, Point(x, y))

    eda.Show(isShowLink=True, isShowBoard=True,isShowKeepout=True)


def JsonMain():
    json_file_path = r"../EDA/Data/GJ_V1.1_0627_mil_group_board_2x.json"
    # 读取输入JSON文件
    with open(json_file_path, 'r') as f:
        data = json.load(f)
    data = ExportJson.convert.GetModuleGraph(data)

    components = data.get("nodes", {})

    G = nx.Graph()
    for node in data["nodes"]:
        width = components[node]["width"]
        height = components[node]["height"]
        if width == 0 or height == 0:
            continue
        G.add_node(node)

    for edge in data["edges"]:
        G.add_edge(edge[0], edge[1])

    board_polygon = data["board"][0]["points"]
    keepout_polygon = data["keepout"][0]["points"]

    wrapped_components = []
    node_to_comp = {}

    for node in G.nodes():
        x = components[node]["x"]
        y = components[node]["y"]
        width = components[node]["width"]
        height = components[node]["height"]
        glued = False

        comp_wrapper = ComponentWrapper(node,glued, width, height, [x,y])
        wrapped_components.append(comp_wrapper)
        node_to_comp[node] = comp_wrapper

    # 保存初始位置用于对比
    initial_pos = {name: (components[name]["x"], components[name]["y"]) for name in components}

    pos = force_directed_layout(G, wrapped_components, node_to_comp, board_polygon, keepout_polygons=None, iterations=8000)

    for name, (x, y) in pos.items():
        components[name]["x"] = x
        components[name]["y"] = y

    # 可视化结果
    visualize_layout_comparison(initial_pos, pos, components, board_polygon, None,G)

    return


def visualize_layout_comparison(initial_pos, final_pos, components, board_polygon,keepout_polygons, G):
    """
    可视化布局优化前后的对比

    Args:
        initial_pos: 初始位置字典
        final_pos: 最终位置字典
        components: 组件信息字典
        board_polygon: 板框多边形
        G: 网络图
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 绘制初始布局
    plot_layout(ax1, initial_pos, components, board_polygon,keepout_polygons, G, "Initial Layout")

    # 绘制优化后布局
    plot_layout(ax2, final_pos, components, board_polygon,keepout_polygons, G, "Optimized Layout")

    plt.tight_layout()
    plt.show()


def plot_layout(ax, positions, components, board_polygon, keepout_polygons,G, title):
    """
    绘制单个布局

    Args:
        ax: matplotlib axes对象
        positions: 位置字典
        components: 组件信息字典
        board_polygon: 板框多边形
        G: 网络图
        title: 图标题
    """
    # 绘制板框
    if board_polygon:
        board_x = [point[0] for point in board_polygon]
        board_y = [point[1] for point in board_polygon]
        ax.plot(board_x + [board_x[0]], board_y + [board_y[0]], 'k-', linewidth=2, label='Board Boundary')

    # 绘制禁布区
    if keepout_polygons:
        for keepout_polygon in keepout_polygons:
            keepout_x = [point[0] for point in keepout_polygon]
            keepout_y = [point[1] for point in keepout_polygon]
            ax.plot(keepout_x + [keepout_x[0]], keepout_y + [keepout_y[0]], 'r-', linewidth=2, label='Keepout Boundary')

    # 绘制连接线
    for edge in G.edges():
        if edge[0] in positions and edge[1] in positions:
            x1, y1 = positions[edge[0]]
            x2, y2 = positions[edge[1]]
            ax.plot([x1, x2], [y1, y2], 'gray', alpha=0.5, linewidth=0.5)

    # 绘制组件
    for name, (x, y) in positions.items():
        if name in components:
            width = components[name]["width"]
            height = components[name]["height"]

            # 创建矩形
            rect = patches.Rectangle(
                (x - width/2, y - height/2), width, height,
                linewidth=1, edgecolor='blue', facecolor='lightblue', alpha=0.7
            )
            ax.add_patch(rect)

            # 添加组件名称
            ax.text(x, y, name, ha='center', va='center', fontsize=8, weight='bold')

    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title(title, fontsize=14, weight='bold')
    ax.legend()

    # # 设置坐标轴范围
    # if board_polygon:
    #     board_x = [point[0] for point in board_polygon]
    #     board_y = [point[1] for point in board_polygon]
    #     margin = max(max(board_x) - min(board_x), max(board_y) - min(board_y)) * 0.1
    #     ax.set_xlim(min(board_x) - margin, max(board_x) + margin)
    #     ax.set_ylim(min(board_y) - margin, max(board_y) + margin)


if __name__ == '__main__':
    EdaMain()
    # JsonMain()