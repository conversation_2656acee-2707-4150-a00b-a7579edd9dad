import numpy as np
import matplotlib.pyplot as plt
from shapely.geometry import Polygon, box
from shapely.affinity import rotate, translate
from shapely.ops import nearest_points
import networkx as nx

class Component:
    def __init__(self, x, y, w, h, angle=0, name=''):
        self.x = x
        self.y = y
        self.w = w
        self.h = h
        self.angle = angle
        self.name = name
        self.update_shape()

    def update_shape(self):
        rect = box(-self.w/2, -self.h/2, self.w/2, self.h/2)
        rotated = rotate(rect, self.angle, origin=(0, 0), use_radians=False)
        self.shape = translate(rotated, self.x, self.y)

    def move(self, dx, dy):
        self.x += dx
        self.y += dy
        self.update_shape()

    def rotate(self, delta_angle):
        self.angle = (self.angle + delta_angle) % 360
        self.update_shape()

def create_components(n, area_bounds):
    components = []
    minx, miny, maxx, maxy = area_bounds
    for i in range(n):
        w, h = np.random.uniform(8, 12), np.random.uniform(4, 6)
        x = np.random.uniform(minx + w/2, maxx - w/2)
        y = np.random.uniform(miny + h/2, maxy - h/2)
        angle = np.random.choice([0, 90])
        comp = Component(x, y, w, h, angle, name=str(i))
        components.append(comp)
    return components

def components_intersect(c1, c2):
    return c1.shape.intersects(c2.shape)

def resolve_collisions(components, max_iter=50):
    n = len(components)
    for iteration in range(max_iter):
        moved = False
        for i in range(n):
            for j in range(i+1, n):
                c1, c2 = components[i], components[j]
                if components_intersect(c1, c2):
                    inter = c1.shape.intersection(c2.shape)
                    if inter.is_empty:
                        continue
                    dx = c1.x - c2.x
                    dy = c1.y - c2.y
                    dist = np.hypot(dx, dy) + 1e-8
                    if dist == 0:
                        dx, dy = 1e-2, 0
                        dist = 1e-2
                    overlap = min(c1.w, c1.h, c2.w, c2.h) / 2
                    shift_x = (dx / dist) * overlap
                    shift_y = (dy / dist) * overlap
                    c1.move(shift_x, shift_y)
                    c2.move(-shift_x, -shift_y)
                    moved = True
        if not moved:
            break

def boundary_constraint(components, boundary):
    for comp in components:
        if not boundary.contains(comp.shape):
            try:
                centroid = comp.shape.centroid
                # 检查centroid是否有效
                if centroid.is_empty or not centroid.is_valid:
                    # 如果centroid无效，使用组件的中心点
                    from shapely.geometry import Point
                    centroid = Point(comp.x, comp.y)

                # 检查boundary是否有效
                if not boundary.is_valid:
                    continue

                nearest_on_boundary, _ = nearest_points(boundary.exterior, centroid)

                # 检查返回的点是否有效
                if nearest_on_boundary.is_empty or not nearest_on_boundary.is_valid:
                    continue

                vec = np.array(centroid.coords[0]) - np.array(nearest_on_boundary.coords[0])
                # 限制移动距离，避免过度移动
                move_distance = np.linalg.norm(vec)
                if move_distance > 0:
                    # 限制最大移动距离
                    max_move = min(comp.w, comp.h) / 2
                    if move_distance > max_move:
                        vec = vec * (max_move / move_distance)
                    comp.move(vec[0], vec[1])

            except Exception as e:
                # 如果出现异常，尝试简单的边界推回策略
                if comp.x < boundary.bounds[0]:
                    comp.move(boundary.bounds[0] - comp.x + comp.w/2, 0)
                elif comp.x > boundary.bounds[2]:
                    comp.move(boundary.bounds[2] - comp.x - comp.w/2, 0)

                if comp.y < boundary.bounds[1]:
                    comp.move(0, boundary.bounds[1] - comp.y + comp.h/2)
                elif comp.y > boundary.bounds[3]:
                    comp.move(0, boundary.bounds[3] - comp.y - comp.h/2)

def force_directed_step(components, graph, k=30, step=0.01):
    n = len(components)
    forces = [np.zeros(2) for _ in range(n)]

    # 吸引力（连边）
    for i, j in graph.edges():
        dx = components[j].x - components[i].x
        dy = components[j].y - components[i].y
        dist = np.hypot(dx, dy) + 1e-8
        direction = np.array([dx, dy]) / dist
        force_mag = (dist**2) / k
        forces[i] += force_mag * direction
        forces[j] -= force_mag * direction

    # 斥力（中心点间）
    for i in range(n):
        for j in range(i+1, n):
            dx = components[i].x - components[j].x
            dy = components[i].y - components[j].y
            dist = np.hypot(dx, dy) + 1e-8
            direction = np.array([dx, dy]) / dist
            force_mag = 1000 / (dist**2)
            forces[i] += force_mag * direction
            forces[j] -= force_mag * direction

    # 更新位置
    for i in range(n):
        move_vec = step * forces[i]
        components[i].move(move_vec[0], move_vec[1])

def plot_layout(components, boundary, title=''):
    fig, ax = plt.subplots(figsize=(10, 10))
    x, y = boundary.exterior.xy
    ax.plot(x, y, 'k--', linewidth=2)
    for c in components:
        x, y = c.shape.exterior.xy
        ax.fill(x, y, alpha=0.6)
        ax.text(c.x, c.y, c.name, fontsize=8, ha='center', va='center')
    ax.set_aspect('equal')
    ax.set_title(title)
    plt.show()

if __name__ == "__main__":
    # 定义边界多边形
    boundary = Polygon([(0,0), (120,20), (140,70), (100,120), (40,100), (10,60)])

    # 创建组件
    components = create_components(80, boundary.bounds)

    # 构建随机连接图
    G = nx.erdos_renyi_graph(30, 0.1)

    # 迭代力导向布局 + 碰撞刚性分离 + 边界约束
    for i in range(2000):
        force_directed_step(components, G)
        resolve_collisions(components)
        boundary_constraint(components, boundary)

    plot_layout(components, boundary, 'Force-Directed Layout + Rigid Collision + Boundary Constraint')
