import os.path
import shutil
import subprocess
import sys
import time

import win32com.client
import json
import logging
from collections import defaultdict
import psutil

logger = logging.getLogger('ExportPadsDataLog')

STARNUM = 30


def kill_process_by_name(process_name):
    """
    杀死任务管理器中指定名称的进程
    :param process_name: 进程名称（如 "notepad.exe"）
    """
    try:
        # 遍历所有进程
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 检查进程名称是否匹配
                if proc.info['name'] == process_name:
                    print(f"找到进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    # 终止进程
                    proc.terminate()
                    print(f"已终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                # 忽略无法访问或已终止的进程
                pass
    except Exception as e:
        print(f"操作失败: {e}")

def setup_logger_SailWind(logpath):
    # 创建日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)  # 设置日志级别
    # 创建日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)  # 控制台输出 DEBUG 及以上级别的日志
    console_handler.setFormatter(formatter)
    # 创建文件处理器
    file_handler = logging.FileHandler(logpath, mode='w')  # 覆盖模式写入日志文件
    file_handler.setLevel(logging.INFO)  # 文件输出 DEBUG 及以上级别的日志
    file_handler.setFormatter(formatter)
    # 将处理器添加到日志记录器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)


def StartSailWind(pads_pcb_file_path, json_folder, unit_flag=2):
    logger.info('START: 提取参数 PADS')
    json_path = ''
    try:
        logger.info("*" * STARNUM)
        print(("*" * STARNUM))
        logger.info(f"START PCBPATH: {pads_pcb_file_path}")
        print(f"START PCBPATH: {pads_pcb_file_path}")
        sailWind = ExportSailWindData(unit_flag)  # 无法使用多线程，因为绑定会有问题
        _, ext = os.path.splitext(pads_pcb_file_path.split("\\")[-1])
        start_time = time.time()
        logger.info(f"START: 运行 SailWindLayout 应用程序")
        print(f"START: 运行 SailWindLayout 应用程序")
        flag = sailWind.ExportData(pads_pcb_file_path)
        logger.info(f"END: 运行 SailWindLayout 应用程序")
        print(f"END: 运行 SailWindLayout 应用程序")
        if flag:
            os.makedirs(json_folder, exist_ok=True)
            json_path = sailWind.CreatrJson(json_folder, _)
        else:
            json_path = ''
        end_time = time.time()
        logger.info(f"{pads_pcb_file_path} 用时 {end_time - start_time} 秒\n")
        print(f"{pads_pcb_file_path} 用时 {end_time - start_time} 秒\n")
    except Exception as e:
        json_path = ''
        logger.error(f'PADS 提取参数失败，原因：{e}')
        print(f'ERROR! PADS 提取参数失败，原因：{e}')
        logger.info("&&" * 40)
    logger.info('END: 提取参数 PADS')
    print('END: 提取参数 PADS')
    return json_path


class ExportSailWindData:
    def __init__(self, unit_flag=2):
        self.unit_flag = unit_flag  # 2: mil; 4: mm
        self.pcbName = None
        self.comp_dict = None
        self.net_dict = None
        self.area_outlines = defaultdict(lambda: defaultdict())
        self.gridX = None
        self.gridY = None
        # 常量
        self.ppcbDrw2Dline = 0      # 2DLine
        self.ppcbDrwBoard = 1       # 板框，包含内外板框
        self.ppcbDrwKeepout = 9     # 禁布区

        self.ppcbObjectTypePolyline = 16    # 多边形
        self.ppcbObjectTypeCircle = 17      # 圆

        self.unit_discribe_dict = {2: "Mils unit (1/1000 of an inch)",
                                   3: "Inch unit",
                                   4: "Metric unit (1/1000 of a meter)"}

        logger.info("初始化 ExportSailWindData\n")
        print("初始化 ExportSailWindData 完成")

    def CreateOpenMacro(self, pcbPath):
        with open(os.path.join(os.getcwd(), 'openCase.mcr'), 'w', encoding="GBK") as file:
            file.write(f'Application.OpenDocument("{pcbPath}")\n')
            file.write('DlgPrompt.Question("作业与当前版本的程序不兼容！").Answer(mbOK)\n')
            file.write("FontReplacementDlg.Ok.Click()\n")
            file.write('DlgPrompt.Question("替换字体会导致设计中的所有文本字符串和标签 转换成新的字体。无法撤消此更改。 确定要继续？").Answer(mbOK)\n')
            file.write('MainFrame.StandardToolbarCurrentLayer = "(H) L01_Top"\n')
            file.write('MainFrame.StandardToolbarCurrentLayer = "(V) L10_Bottom"')
            file.close()
        return os.path.join(os.getcwd(), 'openCase.mcr')

    def ExportData(self, path):
        try:
            # 连接到 SailWindLayout 应用程序
            pads_app = win32com.client.Dispatch("SailWindLayout.Application")
            pads_app.Visible = True  # 使应用程序可见
            logger.info(f'连接到 SailWindLayout 应用程序')
            print(f'连接到 SailWindLayout 应用程序')
        except Exception as e:
            logger.error(f"无法连接到 SailWindLayout 应用程序: {e}")
            print(f"ERROR! 无法连接到 SailWindLayout 应用程序: {e}")
            return

        try:
            # 打开指定的 PCB 文件
            logger.info('*' * STARNUM)
            logger.info(f'Start: 正在打开PCB文件{path}')
            print('*' * STARNUM)
            print(f'Start: 正在打开PCB文件{path}')
            mcr_path = self.CreateOpenMacro(path)
            logger.info(f'End: 打开PCB文件{path}')
            logger.info(f'Start: 正在运行宏文件')
            print(f'End: 打开PCB文件{path}')
            print(f'Start: 正在运行宏文件')
            pads_app.RunMacro(mcr_path, 'openCase')
            logger.info(f'End: 运行宏文件结束')
            logger.info(f'Start: 正在激活文件')
            print(f'End: 运行宏文件结束')
            print(f'Start: 正在激活文件')
            pcb_document = pads_app.ActiveDocument
            logger.info(f'End: 结束激活文件')
            print(f'End: 结束激活文件')
            pcb = pads_app.ActiveDocument
            if not pcb.FullName:
                logger.error(f"PCB文件无效")
                logger.info('*' * STARNUM)
                logger.info(f'Start: 退出 SailWind 模块')
                print(f"ERROR! PCB文件无效")
                print('*' * STARNUM)
                print(f'Start: 退出 SailWind 模块')
                pads_app.Visible = False  # 使应用程序可见
                # pads_app.Quit()

                # process_name = 'Paizi EDA Application'
                # # 使用任务管理器找到进程ID (适用于Windows)
                # output = subprocess.check_output(['tasklist', '/FI', f'IMAGENAME eq {process_name}'],
                #                                  stderr=subprocess.STDOUT,
                #                                  shell=True).decode()
                # pids = [int(line.split()[1]) for line in output.splitlines()[3:] if
                #         process_name.lower() in line.lower()]

                logger.info(f'End: 退出 SailWind 模块结束')
                print(f'End: 退出 SailWind 模块结束')
                logger.info('*' * STARNUM)
                print('*' * STARNUM)
                return False
            logger.info('*' * STARNUM)
            print('*' * STARNUM)

            logger.info('*' * STARNUM)
            logger.info(f'Start: 正在设置单位')
            print('*' * STARNUM)
            print(f'Start: 正在设置单位')
            pcb_document.unit = self.unit_flag
            logger.info(f'End: 设置单位为 {self.unit_discribe_dict[pcb_document.unit]}')
            logger.info('*' * STARNUM)
            print(f'End: 设置单位为 {self.unit_discribe_dict[pcb_document.unit]}')
            print('*' * STARNUM)

            # 获取设计文件名字
            logger.info('*' * STARNUM)
            logger.info(f'Start: 正在获取设计文件名字')
            print('*' * STARNUM)
            print(f'Start: 正在获取设计文件名字')
            self.pcbName = pcb_document.Name
            logger.info(f'End: 获取设计文件名字 {self.pcbName}')
            logger.info('*' * STARNUM)
            print(f'End: 获取设计文件名字 {self.pcbName}')
            print('*' * STARNUM)

            # 获取器件属性
            logger.info('*' * STARNUM)
            logger.info(f'Start: 开始获取器件属性模块')
            print('*' * STARNUM)
            print(f'Start: 开始获取器件属性模块')
            self.comp_dict = {}
            components = pcb_document.Components
            for i in range(1, components.Count + 1):
                component = components.Item(i)
                compName = component.Name             # 器件名
                if compName == 'J6':
                    a = 1
                decalName = component.Decal           # 封装名
                compPartType = component.PartType     # 器件类型
                compGlued = component.Glued
                compPosX = component.CenterX          # 器件中心点坐标
                compPosY = component.CenterY
                compOri = component.Orientation        # 旋转角度
                # compLayer = component.Layer            # 器件所在层数
                compLayer = pcb_document.LayerName(component.Layer)     # 器件所在层名称 TODO:名称？
                compBoxWidth = component.PickBoxWidth     # 器件外包围大小 TODO:包围框是 AABB
                compBoxHeight = component.PickBoxHeight
                compInfo = {"Name": compName,
                            "decalName": decalName,
                            "PartType": compPartType,
                            "IsGlued": compGlued,
                            "PosX": compPosX,
                            "PosY": compPosY,
                            "Orientation": compOri,
                            "Layer": compLayer,
                            "PickBoxWidth": compBoxWidth,
                            "PickBoxHeight": compBoxHeight}
                self.comp_dict[compName] = compInfo

                # 获取引脚信息
                pins = component.pins
                pin_dict = {}
                for j in range(1, pins.Count + 1):
                    cur_pin = {}
                    pin = pins.Item(j)
                    pinName = pin.Name              # 引脚名
                    cur_pin['pinPosX'] = pin.PositionX         # 引脚坐标
                    cur_pin['pinPosY'] = pin.PositionY
                    pin_dict[pinName] = cur_pin
                self.comp_dict[compName]["Pins"] = pin_dict
            logger.info(f'End: 获取器件属性模块结束')
            logger.info('*' * STARNUM)
            print(f'End: 获取器件属性模块结束')
            print('*' * STARNUM)

            # 获取网络信息
            logger.info('*' * STARNUM)
            logger.info(f'Start: 开始获取网络信息模块')
            print('*' * STARNUM)
            print(f'Start: 开始获取网络信息模块')
            self.net_dict = {}
            nets = pcb_document.Nets
            for i in range(1, nets.Count + 1):
                net = nets.Item(i)
                netName = net.Name
                net_pins = net.Pins
                pin_lst = []
                for j in range(1, net_pins.Count + 1):
                    pin = net_pins.Item(j)
                    pinName = pin.Name
                    pinPosX = pin.PositionX
                    pinPosY = pin.PositionY
                    pinInfo = (pinName, pinPosX, pinPosY)
                    pin_lst.append(pinInfo)
                self.net_dict[netName] = pin_lst
            logger.info(f'End: 获取网络信息模块结束')
            logger.info('*' * STARNUM)
            print(f'End: 获取网络信息模块结束')
            print('*' * STARNUM)

            # 获取格点信息
            logger.info('*' * STARNUM)
            logger.info(f'Start: 开始获取格点信息模块')
            print('*' * STARNUM)
            print(f'Start: 开始获取格点信息模块')
            self.gridX = pcb_document.GridX
            self.gridY = pcb_document.GridY
            logger.info(f'End: 获取格点信息模块结束')
            logger.info('*' * STARNUM)

            # 获取板框信息
            logger.info('*' * STARNUM)
            logger.info(f'Start: 开始获取板框信息模块')
            self.CalcBoardOutline(pcb_document)
            logger.info(f'End: 获取板框信息模块结束')
            logger.info('*' * STARNUM)

            logger.info('*' * STARNUM)
            logger.info(f'Start: 保存文件模块')
            pcb_document.save()     # 保存文件，避免弹出是否保存的框 WARNING:会修改pcb文件
            logger.info(f'End: 保存文件模块结束')
            logger.info('*' * STARNUM)

        except Exception as e:
            logger.info('*' * STARNUM)
            logger.info(f'Start: 退出 SailWind 模块')
            logger.error(f"PCB文件提取参数失败: {e}")
            # pads_app.Quit()
            kill_process_by_name('SailWindPCB.exe')
            time.sleep(5)
            logger.info(f'End: 退出 SailWind 模块结束')
            logger.info('*' * STARNUM)
            return False
        logger.info('*' * STARNUM)
        logger.info(f'Start: 退出 SailWind 模块')
        # pads_app.Quit()
        kill_process_by_name('SailWindPCB.exe')
        time.sleep(5)
        logger.info(f'End: 退出 SailWind 模块结束')
        logger.info('*' * STARNUM)
        return True

    # 计算板框信息
    def CalcBoardOutline(self, document):
        # 遍历全部绘图对象,查找板框
        try:
            drawings = document.Drawings
            areaNum = 0
            for i in range(1, drawings.Count + 1):
                drawing = drawings.Item(i)
                drawingType = drawing.DrawingType
                curType = ''
                if self.ppcbDrw2Dline == drawingType:
                    continue
                elif self.ppcbDrwKeepout == drawingType:
                    curType = 'Keepout'
                elif self.ppcbDrwBoard == drawingType:
                    curType = 'Board'
                else:
                    # logger.info(f"未知类型: {drawingType}")
                    continue
                geometrys = drawing.Geometry
                for j in range(1, geometrys.Count + 1):
                    geometry = geometrys.Item(j)
                    curLayer = document.LayerName(geometry.layer)
                    self.area_outlines[areaNum]["Type"] = curType
                    if curType == 'Board':
                        self.area_outlines[areaNum]["Layer"] = 'All'
                    else:
                        self.area_outlines[areaNum]["Layer"] = curLayer
                    if self.ppcbObjectTypePolyline == geometry.ObjectType:
                        geometryType = "Polyline"
                        points = geometry.Points
                        n = len(points)
                        # 遍历顶点数组
                        polyPoints = []
                        for ptI in range(n):
                            polyPoints.append([points[ptI][0], points[ptI][1], points[ptI][2]])
                        self.area_outlines[areaNum][geometryType] = polyPoints
                    elif self.ppcbObjectTypeCircle == geometry.ObjectType:
                        geometryType = "Circle"
                        cricle = [geometry.CenterX, geometry.CenterY, geometry.Radius]
                        self.area_outlines[areaNum][geometryType] = cricle
                    areaNum += 1
        except Exception as e:
            logger.error(f"板框数据获取失败: {e}")
            return

    # 将json文件写到指定目录中
    def CreatrJson(self, dirPath, name):
        # 创建字典
        logger.info('*' * STARNUM)
        logger.info("Start: 开始写入json")
        json_obj = {}
        json_obj["PcbName"] = self.pcbName
        json_obj["Components"] = self.comp_dict
        json_obj["Nets"] = self.net_dict
        json_obj["Area"] = self.area_outlines
        json_obj["GridX"] = self.gridX
        json_obj["GridY"] = self.gridY

        # 将数据写入 JSON 文件
        json_path = os.path.join(dirPath, f"{name}.json")
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(json_obj, f, default=lambda o: list(o) if isinstance(o, set) else o, ensure_ascii=False, indent=4)
            f.close()

        logger.info(f"End: {json_path} 写入完成")
        logger.info('*' * STARNUM)
        return json_path


if __name__=="__main__":
    pads_pcb_file_path = r"D:\Trans\case1\ORI\KK_AY5.134.4508_20230928z.pcb"
    jsonFolder = r"D:\Workspace\ForceDirectedNx\EDA\Data"
    json_path = StartSailWind(pads_pcb_file_path, jsonFolder)
    print(json_path)
