import json
import re

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

def read_board_and_keepout(data):
    """读取并处理PCB的Board和Keepout数据"""
    areas = data.get("Area", {})

    board_data = []
    keepout_data = []

    for area_id, area_info in areas.items():
        area_type = area_info.get("Type", "")

        if area_type == "Board":
            # 处理Board轮廓数据
            if "Polyline" in area_info:
                polyline = area_info["Polyline"]
                # 将polyline数组转换为(x,y)点的格式
                points = []
                for point in polyline:
                    if len(point) >= 2:
                        points.append((point[0], point[1]))  # 只取x,y坐标

                board_data.append({
                    "id": area_id,
                    "type": "Board",
                    "shape": "polyline",
                    "points": points
                })
            elif "Circle" in area_info:
                circle = area_info["Circle"]
                board_data.append({
                    "id": area_id,
                    "type": "Board",
                    "shape": "circle",
                    "center_x": circle[0],
                    "center_y": circle[1],
                    "radius": circle[2]
                })

        elif area_type == "Keepout":
            # 处理Keepout区域数据
            if "Polyline" in area_info:
                polyline = area_info["Polyline"]
                # 将polyline数组转换为(x,y)点的格式
                points = []
                for point in polyline:
                    if len(point) >= 2:
                        points.append((point[0], point[1]))  # 只取x,y坐标

                keepout_data.append({
                    "id": area_id,
                    "type": "Keepout",
                    "shape": "polyline",
                    "points": points
                })
            elif "Circle" in area_info:
                circle = area_info["Circle"]
                keepout_data.append({
                    "id": area_id,
                    "type": "Keepout",
                    "shape": "circle",
                    "center_x": circle[0],
                    "center_y": circle[1],
                    "radius": circle[2]
                })

    return {
        "board": board_data,
        "keepout": keepout_data
    }

def convert_to_module_graph(data):
    """将电路板JSON数据转换为模块级图结构"""
    # 提取分组和网络信息
    groups = data.get("Group", {})
    nets = data.get("Nets", {})
    comps = data.get("Components",{})

    # 创建模块字典，记录每个模块包含的组件
    module_components = {}
    for group_name, subgroups in groups.items():
        module_components[group_name] = []
        for subgroup_name, subgroup_data in subgroups.items():
            module_components[group_name].extend(subgroup_data.get("key", []))
            module_components[group_name].extend(subgroup_data.get("normal", []))

    # 创建组件到模块的映射
    comp_to_module = {}
    for module_name, comp_list in module_components.items():
        for comp_name in comp_list:
            comp_to_module[comp_name] = module_name

    # 计算每个模块的边界矩形
    module_bounds = {}
    for module_name, comp_list in module_components.items():
        all_points = []
        for comp_name in comp_list:
            comp_obj = comps[comp_name]
            # 获取组件的中心点和尺寸
            center_x = comp_obj["PosX"]
            center_y = comp_obj["PosY"]
            width = comp_obj.get("Width", 0)
            height = comp_obj.get("Height", 0)

            # 计算四个角点
            half_width = width / 2
            half_height = height / 2

            # 添加四个角点到all_points列表
            all_points.extend([
                (center_x - half_width, center_y - half_height),  # 左下角
                (center_x + half_width, center_y - half_height),  # 右下角
                (center_x + half_width, center_y + half_height),  # 右上角
                (center_x - half_width, center_y + half_height)   # 左上角
            ])


        if all_points:
            min_x = min(p[0] for p in all_points)
            min_y = min(p[1] for p in all_points)
            max_x = max(p[0] for p in all_points)
            max_y = max(p[1] for p in all_points)

            module_bounds[module_name] = {
                "x": min_x,
                "y": min_y,
                "width": max_x - min_x,
                "height": max_y - min_y,
            }


    # 分析网络连接，找出模块间的连接
    module_connections = {}
    for net_name, pins in nets.items():
        # ignore gnd and power nets
        if 'gnd' in net_name.lower() or re.match(r'^\+\d+(\.\d+)?V$', net_name, re.IGNORECASE):
            continue
        # if 'gnd' in net_name.lower():
        #     continue
        connected_modules = set()
        for pin in pins:
            comp_name = pin[0].split('.')[0]
            if comp_name in comp_to_module:
                connected_modules.add(comp_to_module[comp_name])


        if len(connected_modules) > 1:
            connected_modules_list = list(connected_modules)
            for i in range(len(connected_modules_list)):
                for j in range(i+1, len(connected_modules_list)):
                    mod1 = connected_modules_list[i]
                    mod2 = connected_modules_list[j]
                    connection_key = tuple(sorted([mod1, mod2]))

                    if connection_key not in module_connections:
                        module_connections[connection_key] = []
                    module_connections[connection_key].append(net_name)

    # 读取board和keepout数据
    board_keepout_data = read_board_and_keepout(data)

    # 构建最终的图结构
    graph = {
        "board": board_keepout_data.get("board",[]),
        "keepout": board_keepout_data.get("keepout",[]),
        "nodes": {},
        "edges": []
    }

    # 添加节点（模块）
    for module_name, bounds in module_bounds.items():
        if bounds["width"] == 0 or bounds["height"] == 0:
            continue
        graph["nodes"][module_name] = {
            "id": module_name,
            "name": module_name,
            "x": bounds["x"],
            "y": bounds["y"],
            "width": bounds["width"],
            "height": bounds["height"],
            "bounds": {
                "x": bounds["x"],
                "y": bounds["y"],
                "width": bounds["width"],
                "height": bounds["height"]
            }
        }

    for (mod1, mod2), nets_list in module_connections.items():
        graph["edges"].append([mod1,mod2])

    return graph


def visualize_module_graph(module_graph):
    """可视化模块图"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))

    # 绘制Board边界
    for board in module_graph.get("board", []):
        if board["shape"] == "polyline":
            points = board["points"]
            if len(points) > 2:
                # 将点转换为numpy数组，只取前两个坐标(x, y)
                poly_points = np.array(points)
                if poly_points.shape[1] > 2:
                    poly_points = poly_points[:, :2]  # 只取前两列(x, y)

                # 闭合多边形
                if not np.array_equal(poly_points[0], poly_points[-1]):
                    poly_points = np.vstack([poly_points, poly_points[0]])

                polygon = patches.Polygon(poly_points, linewidth=1,
                                        edgecolor='black', facecolor='lightgray',
                                        alpha=0.5, label='Board')
                ax.add_patch(polygon)
        elif board["shape"] == "circle":
            circle = patches.Circle((board["center_x"], board["center_y"]),
                                   board["radius"], linewidth=1,
                                   edgecolor='black', facecolor='lightgray',
                                   alpha=0.5, label='Board')
            ax.add_patch(circle)

    # 绘制Keepout区域
    for keepout in module_graph.get("keepout", []):
        if keepout["shape"] == "polyline":
            points = keepout["points"]
            if len(points) > 2:
                # 将点转换为numpy数组，只取前两个坐标(x, y)
                poly_points = np.array(points)
                if poly_points.shape[1] > 2:
                    poly_points = poly_points[:, :2]  # 只取前两列(x, y)

                # 闭合多边形
                if not np.array_equal(poly_points[0], poly_points[-1]):
                    poly_points = np.vstack([poly_points, poly_points[0]])

                polygon = patches.Polygon(poly_points, linewidth=1,
                                        edgecolor='red', facecolor='red',
                                        alpha=0.5, label='Keepout')
                ax.add_patch(polygon)
        elif keepout["shape"] == "circle":
            circle = patches.Circle((keepout["center_x"], keepout["center_y"]),
                                   keepout["radius"], linewidth=1,
                                   edgecolor='red', facecolor='red',
                                   alpha=0.5, label='Keepout')
            ax.add_patch(circle)

    # 创建模块位置字典用于绘制连接线
    module_positions = {}

    # 绘制模块节点
    for node_id, node in module_graph["nodes"].items():
        # 计算模块中心点
        center_x = node["x"] + node["width"] / 2
        center_y = node["y"] + node["height"] / 2
        module_positions[node["id"]] = (center_x, center_y)

        # 绘制模块矩形
        rect = patches.Rectangle((node["x"], node["y"]), node["width"], node["height"],
                               linewidth=1, edgecolor='blue', facecolor='lightblue',
                               alpha=0.6)
        ax.add_patch(rect)

        # 添加模块名称标签
        ax.text(center_x, center_y, node["name"],
                ha='center', va='center', fontsize=8, weight='bold')

    # 绘制模块间的连接
    for edge in module_graph["edges"]:
        mod1, mod2 = edge
        if mod1 in module_positions and mod2 in module_positions:
            x1, y1 = module_positions[mod1]
            x2, y2 = module_positions[mod2]

            # 绘制连接线 - 使用更浅的颜色和更细的线条
            ax.plot([x1, x2], [y1, y2], 'g-', linewidth=0.8, alpha=0.3)

    # 设置图形属性
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title('Module Graph Visualization', fontsize=14, weight='bold')
    ax.set_xlabel('X Position')
    ax.set_ylabel('Y Position')

    # 自动调整视图范围
    all_x = []
    all_y = []

    for node_id, node in module_graph["nodes"].items():
        all_x.extend([node["x"], node["x"] + node["width"]])
        all_y.extend([node["y"], node["y"] + node["height"]])

    if all_x and all_y:
        margin = max(max(all_x) - min(all_x), max(all_y) - min(all_y)) * 0.1
        ax.set_xlim(min(all_x) - margin, max(all_x) + margin)
        ax.set_ylim(min(all_y) - margin, max(all_y) + margin)

    plt.tight_layout()
    plt.show()



def GetModuleGraph(data):
    return convert_to_module_graph(data)

def main():
    json_file_path = r"../EDA/Data/GJ_V1.1_0627_mil_group.json"
    # 读取输入JSON文件
    with open(json_file_path, 'r') as f:
        data = json.load(f)

    # 转换为模块图结构
    module_graph = convert_to_module_graph(data)

    # 将结果写入输出JSON文件
    with open('module_graph.json', 'w') as f:
        json.dump(module_graph, f, indent=2)

    # 添加可视化
    print("正在生成模块图可视化...")
    visualize_module_graph(module_graph)
    print("可视化完成！")

if __name__ == "__main__":
    main()
