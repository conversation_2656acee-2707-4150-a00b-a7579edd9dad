import numpy as np
from EasyEDA import EasyEDA
from BaseDefine import Point
import networkx as nx
import random


def custom_spring_layout_with_real_coords(G, components, board, pos=None, iterations=500, k=None, repulsion_strength=1000.0):

    # 转换为numpy数组便于计算
    nodes = list(G.nodes())
    positions = np.array([[pos[node][0], pos[node][1]] for node in nodes])
    node_to_idx = {node: i for i, node in enumerate(nodes)}

    # 获取组件尺寸信息（实际尺寸）
    board_w, board_h = board.GetWidth(), board.GetHeight()
    comp_sizes = np.array([[components[node].boxSize.width, components[node].boxSize.height] for node in nodes])

    # 计算k值（如果未提供）
    if k is None:
        avg_comp_size = np.mean(comp_sizes)
        k = avg_comp_size * 2.0

    def rectangles_overlap(pos1, size1, pos2, size2, margin=5.0):
        """检查两个矩形是否重叠（考虑安全边距）"""
        x1, y1 = pos1
        w1, h1 = size1
        x2, y2 = pos2
        w2, h2 = size2

        # 矩形边界
        left1, right1 = x1 - w1/2 - margin, x1 + w1/2 + margin
        bottom1, top1 = y1 - h1/2 - margin, y1 + h1/2 + margin
        left2, right2 = x2 - w2/2 - margin, x2 + w2/2 + margin
        bottom2, top2 = y2 - h2/2 - margin, y2 + h2/2 + margin

        # 检查重叠
        return not (right1 <= left2 or right2 <= left1 or top1 <= bottom2 or top2 <= bottom1)

    def calculate_strong_repulsion_force(pos1, size1, pos2, size2):
        """使用库伦定律计算排斥力，距离采用曼哈顿距离"""
        x1, y1 = pos1
        w1, h1 = size1
        x2, y2 = pos2
        w2, h2 = size2

        # 计算中心点之间的曼哈顿距离
        dx = x2 - x1
        dy = y2 - y1
        manhattan_distance = abs(dx) + abs(dy)

        # # 防止除零错误
        # if manhattan_distance < 1e-6:
        #     # 完全重合，随机方向推开
        #     angle = random.random() * 2 * np.pi
        #     dx = np.cos(angle) * 50
        #     dy = np.sin(angle) * 50
        #     manhattan_distance = abs(dx) + abs(dy)

        # 计算理想的最小曼哈顿距离（基于组件尺寸）
        min_manhattan_distance = (w1 + w2)/2 + (h1 + h2)/2 + 20

        # 使用库伦定律：F = k * q1 * q2 / r^2
        # 这里假设每个组件的"电荷"与其面积成正比
        charge1 = w1 * h1  # 组件1的"电荷"
        charge2 = w2 * h2  # 组件2的"电荷"

        # 库伦常数，可以调整来控制排斥力强度
        coulomb_constant = repulsion_strength * 1000

        # 计算库伦力大小
        # 使用曼哈顿距离的平方作为分母
        coulomb_force_magnitude = coulomb_constant / (manhattan_distance ** 2)

        # 如果距离小于最小距离，增强排斥力
        if manhattan_distance < min_manhattan_distance:
            # 距离越小，排斥力越强
            distance_factor = (min_manhattan_distance / manhattan_distance) ** 2
            coulomb_force_magnitude *= distance_factor

        # 计算力的方向（沿着中心连线方向）
        if manhattan_distance > 1e-6:
            # 方向向量（单位化）
            direction_x = dx / manhattan_distance if abs(dx) > 1e-6 else 0
            direction_y = dy / manhattan_distance if abs(dy) > 1e-6 else 0

            # 应用库伦力
            force_x = coulomb_force_magnitude * direction_x
            force_y = coulomb_force_magnitude * direction_y

            return np.array([force_x, force_y])

        return np.array([0.0, 0.0])

    # 记录历史最佳重叠数
    best_overlap_count = float('inf')
    best_positions = None
    stagnant_iterations = 0

    for iteration in range(iterations):
        forces = np.zeros_like(positions)

        # 1. 弹簧引力（连接的组件之间）- 使用胡克定律
        for edge in G.edges():
            i = node_to_idx[edge[0]]
            j = node_to_idx[edge[1]]

            diff = positions[j] - positions[i]
            # 使用曼哈顿距离计算距离
            manhattan_distance = abs(diff[0]) + abs(diff[1])

            if manhattan_distance > 1e-6:
                # 理想距离：基于组件尺寸的曼哈顿距离
                ideal_manhattan_distance = (np.mean(comp_sizes[i]) + np.mean(comp_sizes[j])) * 1.5 + 50

                # 胡克定律：F = -k * x，其中x是形变量（实际距离 - 理想距离）
                # 形变量：正值表示拉伸，负值表示压缩
                deformation = manhattan_distance - ideal_manhattan_distance

                # 胡克力大小
                hooke_force_magnitude = k * abs(deformation) * 0.001

                # 力的方向：如果拉伸则相互吸引，如果压缩则相互排斥
                if deformation > 0:
                    # 拉伸状态，产生吸引力
                    force_direction = -diff / manhattan_distance  # 指向对方
                else:
                    # 压缩状态，产生排斥力
                    force_direction = diff / manhattan_distance   # 远离对方

                force = hooke_force_magnitude * force_direction
                forces[i] += force
                forces[j] -= force

        # 2. 强排斥力（防止重叠）
        for i in range(len(nodes)):
            for j in range(i + 1, len(nodes)):
                repulsion_force = calculate_strong_repulsion_force(
                    positions[i], comp_sizes[i],
                    positions[j], comp_sizes[j]
                )

                forces[i] -= repulsion_force
                forces[j] += repulsion_force

        # 3. 边界约束力
        for i, node in enumerate(nodes):
            w, h = comp_sizes[i]
            x, y = positions[i]

            boundary_strength = k * 10.0
            margin = 10.0

            # 边界约束
            min_x = board.left + w/2 + margin
            max_x = board.left + board_w - w/2 - margin
            min_y = board.bottom + h/2 + margin
            max_y = board.bottom + board_h - h/2 - margin

            if x < min_x:
                forces[i][0] += boundary_strength * (min_x - x)
            elif x > max_x:
                forces[i][0] -= boundary_strength * (x - max_x)

            if y < min_y:
                forces[i][1] += boundary_strength * (min_y - y)
            elif y > max_y:
                forces[i][1] -= boundary_strength * (y - max_y)

        # 4. 更新位置
        max_force = np.max(np.abs(forces))
        if max_force > 0:
            step_size = min(5.0, 50.0 / max_force)
            positions += forces * step_size

        # 5. 硬边界约束
        for i, node in enumerate(nodes):
            w, h = comp_sizes[i]
            min_x = board.left + w/2 + 5
            max_x = board.left + board_w - w/2 - 5
            min_y = board.bottom + h/2 + 5
            max_y = board.bottom + board_h - h/2 - 5

            positions[i][0] = np.clip(positions[i][0], min_x, max_x)
            positions[i][1] = np.clip(positions[i][1], min_y, max_y)

        # 6. 检查重叠情况
        overlap_count = 0
        for i in range(len(nodes)):
            for j in range(i + 1, len(nodes)):
                if rectangles_overlap(positions[i], comp_sizes[i], positions[j], comp_sizes[j]):
                    overlap_count += 1

        # 记录最佳状态
        if overlap_count < best_overlap_count:
            best_overlap_count = overlap_count
            best_positions = positions.copy()
            stagnant_iterations = 0
        else:
            stagnant_iterations += 1

        # 7. 收敛检查
        if overlap_count == 0:
            print(f"布局成功！第 {iteration} 次迭代达到无重叠状态")
            break

        # 如果长时间无改进，增强排斥力
        if stagnant_iterations > 50:
            repulsion_strength *= 1.2
            stagnant_iterations = 0
            print(f"增强排斥力到 {repulsion_strength:.2f}")

        # 打印进度
        if iteration % 100 == 0:
            print(f"迭代 {iteration}, 重叠数: {overlap_count}, 最佳: {best_overlap_count}, 最大力: {max_force:.2f}")

    # 使用最佳结果
    if best_positions is not None:
        positions = best_positions

    print(f"最终重叠数: {best_overlap_count}")

    # 转换回字典格式
    result_pos = {nodes[i]: tuple(positions[i]) for i in range(len(nodes))}
    return result_pos


if __name__ == '__main__':
    eda = EasyEDA()
    eda.InitEnv('Data/single_side/0.json')

    comps = eda.comps
    nets = eda.nets
    board = eda.board
    G = nx.Graph()

    # 添加节点
    for name in comps:
        G.add_node(name)

    # 添加边（根据pinPairs）
    for net in nets.values():
        if eda.IsGNDorPowerNet(net.netName):
           continue
        for pin1, pin2 in net.pinPairs:
            comp1 = pin1.split('.')[0]
            comp2 = pin2.split('.')[0]
            if comp1 != comp2:
                G.add_edge(comp1, comp2)

    # 使用当前位置作为初始位置
    init_pos = {name: (comp.pos.x, comp.pos.y) for name, comp in comps.items()}

    # 使用自定义布局算法（直接实际坐标）
    pos = custom_spring_layout_with_real_coords(G, comps, board, pos=init_pos, iterations=5000)

    # 直接应用结果位置
    for name, (x, y) in pos.items():
        eda.MoveComp(name, Point(x, y))


    eda.Show(isShowLink=True, isShowBoard=True)
