cmake_minimum_required(VERSION 3.10)
project(ForceLayout)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)


set(CMAKE_PREFIX_PATH "C:/Qt/5.15.2/msvc2019_64")


#find_package(Qt5 COMPONENTS Core Gui REQUIRED)

add_executable(ForceLayout
        src/main.cc
        src/graph.cc
        src/graph.h
        src/force_directed.cc
        src/force_directed.h
        src/force_model.h
        src/force_model.cc
        src/vector2d.h
        src/polygon_utils.h
        src/polygon_utils.cc
)

# 添加测试程序
add_executable(TestForceLayout
        src/test_force_directed.cc
        src/graph.cc
        src/graph.h
        src/force_directed.cc
        src/force_directed.h
        src/force_model.h
        src/force_model.cc
        src/vector2d.h
        src/polygon_utils.h
        src/polygon_utils.cc
)

# 添加示例程序
add_executable(ExampleForceLayout
        src/example_usage.cc
        src/graph.cc
        src/graph.h
        src/force_directed.cc
        src/force_directed.h
        src/force_model.h
        src/force_model.cc
        src/vector2d.h
        src/polygon_utils.h
        src/polygon_utils.cc
)

# 添加可视化示例程序
add_executable(VisualizationExample
        src/visualization_example.cc
        src/graph.cc
        src/graph.h
        src/force_directed.cc
        src/force_directed.h
        src/force_model.h
        src/force_model.cc
        src/vector2d.h
        src/polygon_utils.h
        src/polygon_utils.cc
        src/svg_visualizer.cc
        src/svg_visualizer.h
)

target_link_libraries(ForceLayout )
target_link_libraries(TestForceLayout )
target_link_libraries(ExampleForceLayout )
target_link_libraries(VisualizationExample )