# 力导向算法测试文档

## 概述

本项目实现了一个完整的力导向布局算法，用于电路板组件的自动布局优化。该算法通过模拟物理力（斥力和引力）来优化组件位置，减少重叠并改善布局质量。

## 测试程序

### 1. 单元测试程序 (`TestForceLayout`)

**功能**: 全面测试算法的各个组件
**文件**: `src/test_force_directed.cc`

#### 测试覆盖范围:
- ✅ **Vector2D Operations**: 向量运算（加法、减法、缩放、模长、归一化）
- ✅ **Graph Structure**: 图结构（节点、边的创建和管理）
- ✅ **Repulsive Force Model**: 斥力模型（节点间排斥力计算）
- ✅ **Attractive Force Model**: 引力模型（连接节点间的吸引力）
- ✅ **Configuration**: 配置系统（参数设置和修改）
- ✅ **ForceDirected Algorithm**: 主算法（完整的力导向布局流程）
- ✅ **Performance Test**: 性能测试（50个节点，100次迭代）

#### 运行方式:
```bash
cd cmake-build-debug
cmake --build . --target TestForceLayout
./TestForceLayout
```

#### 预期输出:
```
=== Force Directed Layout Algorithm Test Suite ===

[PASS] Vector2D Operations
[PASS] Graph Structure
[PASS] Repulsive Force Model
[PASS] Attractive Force Model
[PASS] Configuration
[PASS] ForceDirected Algorithm

=== Test Summary ===
Passed: 6/6
All tests passed! ✓

Running performance test...
Performance test completed in XX ms
[PASS] Performance Test
```

### 2. 示例程序 (`ExampleForceLayout`)

**功能**: 演示如何使用力导向算法
**文件**: `src/example_usage.cc`

#### 功能特性:
- 创建包含6个节点的示例图
- 配置算法参数
- 运行力导向布局优化
- 分析布局结果
- 检查组件重叠
- 计算能量变化和移动距离

#### 运行方式:
```bash
cd cmake-build-debug
cmake --build . --target ExampleForceLayout
./ExampleForceLayout
```

#### 预期输出:
```
=== Force Directed Layout Example ===

Creating example graph with 6 nodes...
  Component_1 at (0, 0) size: 10x8
  Component_2 at (45, 0) size: 15x12
  ...

=== Initial State ===
Component_1: position(0, 0), force(0, 0), |force|=0
...

=== Configuring Force Directed Algorithm ===
Algorithm configuration:
  Iterations: 200
  Damping: 0.9
  Max velocity: 15
  ...

=== Running Force Directed Layout ===
Starting layout optimization...
Iteration 0, Energy: XXXX
...
Layout completed in XX ms

=== Final State ===
Component_1: position(X, Y), force(X, Y), |force|=X
...

=== Layout Analysis ===
Component_1 moved X units
...
Average movement: X units
Maximum movement: X units

=== Overlap Check ===
No overlaps detected - layout is valid!

=== Example Complete ===
```

## 算法核心组件

### 1. 力模型 (Force Models)

#### 斥力模型 (RepulsiveForce)
- **目的**: 防止组件重叠
- **原理**: 基于库仑定律，距离越近斥力越大
- **参数**: `k_repulsive` (斥力系数)

#### 引力模型 (AttractiveForce)
- **目的**: 使连接的组件靠近
- **原理**: 基于胡克定律，类似弹簧力
- **参数**: `k_attractive` (引力系数)

### 2. 配置参数 (Config)

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `iterations` | 800 | 最大迭代次数 |
| `damping` | 0.9 | 阻尼系数（0-1） |
| `max_velocity` | 10.0 | 最大移动速度 |
| `temperature` | 1.0 | 初始温度 |
| `cooling_rate` | 0.99 | 冷却速率 |
| `energy_threshold` | 1e-3 | 收敛阈值 |

### 3. 图结构 (Graph)

#### 节点 (Node)
- `id`: 唯一标识符
- `position`: 位置坐标
- `width/height`: 组件尺寸
- `forces`: 当前受力
- `velocity`: 移动速度
- `is_fixed`: 是否固定位置

#### 边 (Edge)
- `id`: 唯一标识符
- `source/target`: 连接的两个节点

## 编译和构建

### 前置要求
- CMake 3.10+
- C++14 编译器
- 支持的平台: Linux, macOS, Windows

### 构建步骤
```bash
# 1. 创建构建目录
mkdir cmake-build-debug
cd cmake-build-debug

# 2. 配置项目
cmake ..

# 3. 编译所有目标
cmake --build .

# 或者编译特定目标
cmake --build . --target TestForceLayout
cmake --build . --target ExampleForceLayout
cmake --build . --target ForceLayout
```

## 性能指标

基于测试结果:
- **小规模图** (6个节点): < 10ms
- **中等规模图** (50个节点): ~56ms
- **内存使用**: 低内存占用
- **收敛性**: 通常在100-200次迭代内收敛

## 使用建议

### 参数调优
1. **斥力系数** (`k_repulsive`): 
   - 增大可减少重叠，但可能导致布局过于分散
   - 建议范围: 1000-5000

2. **引力系数** (`k_attractive`):
   - 增大可使连接组件更紧密
   - 建议范围: 0.05-0.5

3. **迭代次数** (`iterations`):
   - 根据图的复杂度调整
   - 简单图: 100-300次
   - 复杂图: 500-1000次

### 最佳实践
1. 先运行测试程序确保算法正常工作
2. 使用示例程序了解基本用法
3. 根据具体应用场景调整参数
4. 监控能量变化判断收敛状态
5. 检查最终布局的重叠情况

## 故障排除

### 常见问题
1. **编译错误**: 检查C++14支持和CMake版本
2. **测试失败**: 检查算法实现和参数设置
3. **性能问题**: 减少迭代次数或优化力模型参数
4. **布局质量差**: 调整力模型参数或增加迭代次数

### 调试技巧
1. 使用测试程序验证各组件功能
2. 监控能量变化曲线
3. 检查节点移动距离
4. 分析力的分布情况

## 扩展功能

算法支持以下扩展:
- 边界约束力 (BoundaryRepulsiveForce)
- 禁布区约束力 (KeepoutRepulsiveForce)
- 拓扑保持力 (TopologyForce)
- 自定义力模型

可以通过继承 `ForceModel` 基类来实现自定义力模型。
