#!/bin/bash

# 力导向算法测试运行脚本

echo "=== 力导向算法测试套件 ==="
echo ""

# 检查构建目录
if [ ! -d "cmake-build-debug" ]; then
    echo "创建构建目录..."
    mkdir cmake-build-debug
fi

cd cmake-build-debug

# 配置项目
echo "配置项目..."
cmake .. > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败"
    exit 1
fi

echo "✅ CMake配置成功"

# 编译测试程序
echo ""
echo "编译测试程序..."
cmake --build . --target TestForceLayout > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ 测试程序编译失败"
    exit 1
fi

echo "✅ 测试程序编译成功"

# 运行单元测试
echo ""
echo "=== 运行单元测试 ==="
./TestForceLayout

if [ $? -ne 0 ]; then
    echo "❌ 单元测试失败"
    exit 1
fi

echo ""
echo "✅ 单元测试全部通过"

# 编译示例程序
echo ""
echo "编译示例程序..."
cmake --build . --target ExampleForceLayout > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ 示例程序编译失败"
    exit 1
fi

echo "✅ 示例程序编译成功"

# 运行示例程序
echo ""
echo "=== 运行示例程序 ==="
./ExampleForceLayout

if [ $? -ne 0 ]; then
    echo "❌ 示例程序运行失败"
    exit 1
fi

echo ""
echo "✅ 示例程序运行成功"

echo ""
echo "=== 测试总结 ==="
echo "✅ 所有测试都已通过"
echo "✅ 力导向算法工作正常"
echo "✅ 可以开始使用算法进行布局优化"

echo ""
echo "下一步建议："
echo "1. 查看 README_TESTING.md 了解详细使用说明"
echo "2. 根据具体需求调整算法参数"
echo "3. 集成到你的应用程序中"

cd ..
