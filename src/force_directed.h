#ifndef FORCELAYOUT_FORCE_DIRECTED_H
#define FORCELAYOUT_FORCE_DIRECTED_H

#include "force_model.h"
#include "graph.h"

namespace force_directed {

struct Config {
  int iterations = 800;
  double time_step = 0.1;
  double damping = 0.9;
  double max_velocity = 10.0;
  double temperature = 1.0;
  double cooling_rate = 0.99;
  double energy_threshold = 1e-3;
  double repulsive_strength = 200.0;
  double attractive_strength = 0.05;
};

class ForceDirected {
 public:
  explicit ForceDirected(const Graph& graph);
  ForceDirected() = default;

  void Run();
  void AddForceModel(std::unique_ptr<ForceModel>&& model) {
    force_models_.push_back(std::move(model));
  }
  void SetForceModels(std::vector<std::unique_ptr<ForceModel>>&& models) {
    force_models_ = std::move(models);
  }

  //getter and setter
  void set_graph(const Graph& graph) { graph_ = graph; }
  void set_config(const Config& config) { config_ = config; }
  Graph graph() { return graph_; }

 private:
  Graph graph_;
  Config config_;
  double last_energy_ {};
  double current_temperature_ {};
  std::vector<std::unique_ptr<ForceModel>> force_models_;

  double Energy() const;

  // 计算所有力模型产生的力
  void ApplyForces();
  void UpdatePositions(int iter_num, double step = 0.1);

};

}  // namespace force_directed
#endif  // FORCELAYOUT_FORCE_DIRECTED_H
