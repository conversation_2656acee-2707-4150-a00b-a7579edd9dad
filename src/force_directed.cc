#include "force_directed.h"
#include <random>

namespace force_directed {

ForceDirected::ForceDirected(const Graph& graph) {
  graph_ = graph;
  current_temperature_ = config_.temperature;
}

void ForceDirected::Run() {
  current_temperature_ = config_.temperature;
  for (int i = 0; i < config_.iterations; i++) {

    // 1.calculate force
    ApplyForces();
    // 2.update position
    UpdatePositions(i, 0.1);

    if (i % 10 == 0) {
      // 3.calculate energy
      const double current_energy = Energy();
      // 4.is converged
      if (std::abs(current_energy - last_energy_) < config_.energy_threshold) {
        printf("Converged at iteration %d, Energy: %f\n", i, current_energy);
        break;
      }
      last_energy_ = current_energy;

      if (i % 100 == 0) {
        printf("Iteration %d, Energy: %f\n", i, current_energy);
      }
    }
  }
}

void ForceDirected::ApplyForces() {
  // reset all forces
  for (const auto& node : graph_.nodes()) {
    node->forces = Vector2D{0.0, 0.0};
  }

  for (const auto& force:force_models_) {
    force->Apply(graph_);
  }
}

// calculate new position
// todo: optimize: only update node which force is changed
void ForceDirected::UpdatePositions(int iter_num, double step) {

  // temperature cooling
  if (iter_num < config_.iterations * 0.01) {
    current_temperature_ *= 0.99;
  } else if (iter_num < config_.iterations * 0.5) {
    current_temperature_ *= 0.997;
  } else {
    current_temperature_ *= config_.cooling_rate;
  }

  for (const auto& node : graph_.nodes()) {
    if (node->is_fixed) {
      continue;
    }

    auto acceleration = node->forces * std::min(1.0, current_temperature_);
    // add random force,
    if (current_temperature_ > 0.3) {
      std::random_device rd;
      std::mt19937 generator(rd());
      std::normal_distribution<double> dist(0.0, current_temperature_ * 0.5);
      const auto random_force = Vector2D{dist(generator), dist(generator)};
      acceleration += random_force;
    }
    node->velocity = node->velocity * config_.damping + acceleration * step;
    const auto velocity_mag = node->velocity.Magnitude();
    if (velocity_mag > config_.max_velocity) {
      node->velocity = node->velocity / velocity_mag * config_.max_velocity;
    }
    node->position += node->velocity * step;
  }
}

double ForceDirected::Energy() const {
  double sum = 0.0;
  for (const auto& force : force_models_) {
    sum += force->Energy(graph_);
  }
  return sum;
}

}  // namespace force_directed
