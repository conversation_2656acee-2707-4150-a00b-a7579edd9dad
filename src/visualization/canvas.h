#ifndef CANVAS_H
#define CANVAS_H

#include <QPainterPath>
#include <vector>
#include <QGraphicsView>


struct Curve {
    QPointF point;
    double radius;
    Curve(QPointF p, double r) : point(p), radius(r) {}
};

struct ArcParams {
    QPointF center_point{};
    double abs_radius{};
    double theta1{}, theta2{};
    bool valid;

    ArcParams() : valid(false) {}
    ArcParams(QPointF point,double r,double t1,double t2)
        :center_point(point),abs_radius(r), theta1(t1), theta2(t2), valid(true) {
    }
};

class Canvas : public QGraphicsView {
    Q_OBJECT
    using Polygon = std::vector<Curve> ;
    using Polygons = std::vector<Polygon> ;
public:
    Canvas();
    void Show();
    void AutoScaleToFit();
    void AddPolygon(const Polygons& polygons, const QColor& fill_color, const QColor &outer_color);
    void CreateGraphicsItem(const QPainterPath& painter_path, const QColor &fill_color, const QColor& outer_color) const;

protected:
    void wheelEvent(QWheelEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private:
    static ArcParams GetArcParams(QPointF point_0,QPointF point_1, double radius);
    static QPainterPath CreatePath(const Polygons &polygons);

    bool is_panning_ = false;
    QPoint last_mouse_pos_;
    QGraphicsScene  *canvas_;
};




#endif //CANVAS_H