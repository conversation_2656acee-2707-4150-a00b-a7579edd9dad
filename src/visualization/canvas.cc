// canvas.cc
// 修正版：正确处理 arcTo 的角度方向

#include "../canvas.h"

#include <QGraphicsPathItem>
#include <QMouseEvent>
#include <QPen>
#include <QtMath>

Canvas::Canvas() {
    canvas_ = new QGraphicsScene(this);
    this->setScene(canvas_);
    this->setRenderHint(QPainter::Antialiasing);
    this->setRenderHint(QPainter::SmoothPixmapTransform);
    this->setDragMode(NoDrag);
    this->setViewportUpdateMode(SmartViewportUpdate);
    //反转Y轴
    this->scale(1,-1);
}

void Canvas::Show() {
    AutoScaleToFit();
    this->show();
}

void Canvas::AutoScaleToFit() {
    QRectF items_rect = canvas_->itemsBoundingRect();
    this->fitInView(items_rect, Qt::KeepAspectRatio);
}

void Canvas::AddPolygon(const Polygons& polygons, const QColor& fill_color, const QColor &outer_color) {
    CreateGraphicsItem(CreatePath(polygons), fill_color, outer_color);
}

void Canvas::CreateGraphicsItem(const QPainterPath& painter_path, const QColor &fill_color, const QColor& outer_color) const {
    auto item = new QGraphicsPathItem(painter_path);
    canvas_->addItem(item);
    item->setPen(QPen(outer_color, 0));
    item->setBrush(QBrush(fill_color));
    item->setFlag(QGraphicsItem::ItemIsMovable);
}

ArcParams Canvas::GetArcParams(QPointF point_0, QPointF point_1, double radius) {
    double x0 = point_0.x();
    double y0 = point_0.y();
    double x1 = point_1.x();
    double y1 = point_1.y();

    double chord_length = std::hypot(x1 - x0, y1 - y0);
    if (std::abs(radius) < chord_length / 2) {
        return {};
    }

    double mid_x = (x0 + x1) / 2;
    double mid_y = (y0 + y1) / 2;
    double abs_radius = std::abs(radius);
    double h = std::sqrt(abs_radius * abs_radius - (chord_length / 2) * (chord_length / 2));

    double chord_dx = x1 - x0;
    double chord_dy = y1 - y0;
    double perp_dx = chord_dy / chord_length;
    double perp_dy = chord_dx / chord_length;

    double center_x, center_y;
    if (radius < 0) {
        center_x = mid_x - h * perp_dx;
        center_y = mid_y + h * perp_dy;
    } else {
        center_x = mid_x + h * perp_dx;
        center_y = mid_y - h * perp_dy;
    }

    double start_angle = std::atan2(y0 - center_y, x0 - center_x) * 180.0 / M_PI;
    double end_angle = std::atan2(y1 - center_y, x1 - center_x) * 180.0 / M_PI;

    return {{center_x, center_y}, abs_radius, start_angle, end_angle};
}

QPainterPath Canvas::CreatePath(const Polygons &polygons) {
    QPainterPath path;

    if (polygons.empty()) return path;

    for (const auto& curves : polygons) {
        if (curves.empty()) continue;

        auto start_point = curves[0].point;
        path.moveTo(start_point);

        for (size_t i = 0; i < curves.size() - 1; ++i) {
            double radius = curves[i].radius;
            QPointF point_next = curves[i + 1].point;

            if (radius == 0) {
                path.lineTo(point_next);
            } else {
                ArcParams arc = GetArcParams(curves[i].point, point_next, radius);
                if (arc.valid) {
                    QPointF center = arc.center_point;
                    double sr = arc.abs_radius;
                    QRectF rect(center.x() - sr, center.y() - sr, 2 * sr, 2 * sr);

                    // 翻转角度适配 Qt 坐标
                    double qt_theta1 = -arc.theta1;
                    double qt_theta2 = -arc.theta2;

                    double span = qt_theta2 - qt_theta1;
                    if (span <= 0) span += 360;

                    if (radius < 0) {
                        // 逆时针绘制，Qt 需要负方向
                        span = span - 360;  // span 负数，Qt 绘制逆时针
                    }

                    // 绘制弧段
                    path.arcTo(rect, qt_theta1, span);
                } else {
                    path.lineTo(point_next);
                }
            }
        }
        path.closeSubpath();
    }
    return path;
}


void Canvas::wheelEvent(QWheelEvent *event)
{
    constexpr double zoomFactor = 1.15;
    if (event->angleDelta().y() > 0) {
        scale(zoomFactor, zoomFactor);
    } else {
        scale(1.0 / zoomFactor, 1.0 / zoomFactor);
    }
}

void Canvas::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        is_panning_ = true;
        last_mouse_pos_ = event->pos();
        setCursor(Qt::ClosedHandCursor);
    }
    QGraphicsView::mousePressEvent(event);
}

void Canvas::mouseMoveEvent(QMouseEvent *event)
{
    if (is_panning_) {
        QPointF delta = mapToScene(event->pos()) - mapToScene(last_mouse_pos_);
        translate(-delta.x(), -delta.y());
        last_mouse_pos_ = event->pos();
    }
    QGraphicsView::mouseMoveEvent(event);
}

void Canvas::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        is_panning_ = false;
        setCursor(Qt::ArrowCursor);
    }
    QGraphicsView::mouseReleaseEvent(event);
}
