#include <iostream>
#include <memory>
#include <cassert>
#include <cmath>
#include <vector>
#include <string>
#include <chrono>

#include "force_directed.h"
#include "force_model.h"
#include "graph.h"
#include "vector2d.h"

using namespace force_directed;

// 测试辅助函数
void PrintTestResult(const std::string& test_name, bool passed) {
    std::cout << "[" << (passed ? "PASS" : "FAIL") << "] " << test_name << std::endl;
}

// 创建测试图的辅助函数
Graph CreateSimpleGraph() {
    Graph graph;
    
    // 创建4个节点
    auto node1 = std::make_shared<Node>();
    node1->id = "node1";
    node1->position = Vector2D{0.0, 0.0};
    node1->width = 10.0;
    node1->height = 10.0;
    
    auto node2 = std::make_shared<Node>();
    node2->id = "node2";
    node2->position = Vector2D{50.0, 0.0};
    node2->width = 10.0;
    node2->height = 10.0;
    
    auto node3 = std::make_shared<Node>();
    node3->id = "node3";
    node3->position = Vector2D{25.0, 50.0};
    node3->width = 10.0;
    node3->height = 10.0;
    
    auto node4 = std::make_shared<Node>();
    node4->id = "node4";
    node4->position = Vector2D{25.0, -50.0};
    node4->width = 10.0;
    node4->height = 10.0;
    
    graph.add_node(node1);
    graph.add_node(node2);
    graph.add_node(node3);
    graph.add_node(node4);
    
    // 创建边：形成一个简单的连通图
    auto edge1 = std::make_shared<Edge>();
    edge1->id = "edge1";
    edge1->source = node1;
    edge1->target = node2;
    
    auto edge2 = std::make_shared<Edge>();
    edge2->id = "edge2";
    edge2->source = node2;
    edge2->target = node3;
    
    auto edge3 = std::make_shared<Edge>();
    edge3->id = "edge3";
    edge3->source = node3;
    edge3->target = node1;
    
    auto edge4 = std::make_shared<Edge>();
    edge4->id = "edge4";
    edge4->source = node1;
    edge4->target = node4;
    
    graph.add_edge(edge1);
    graph.add_edge(edge2);
    graph.add_edge(edge3);
    graph.add_edge(edge4);
    
    return graph;
}

// 测试Vector2D类
bool TestVector2D() {
    Vector2D v1{3.0, 4.0};
    Vector2D v2{1.0, 2.0};
    
    // 测试基本运算
    Vector2D sum = v1 + v2;
    bool addition_test = (std::abs(sum.x - 4.0) < 1e-6) && (std::abs(sum.y - 6.0) < 1e-6);
    
    Vector2D diff = v1 - v2;
    bool subtraction_test = (std::abs(diff.x - 2.0) < 1e-6) && (std::abs(diff.y - 2.0) < 1e-6);
    
    Vector2D scaled = v1 * 2.0;
    bool scaling_test = (std::abs(scaled.x - 6.0) < 1e-6) && (std::abs(scaled.y - 8.0) < 1e-6);
    
    // 测试模长
    double magnitude = v1.Magnitude();
    bool magnitude_test = std::abs(magnitude - 5.0) < 1e-6;
    
    // 测试归一化
    Vector2D normalized = v1.Normalize();
    bool normalize_test = std::abs(normalized.Magnitude() - 1.0) < 1e-6;
    
    return addition_test && subtraction_test && scaling_test && magnitude_test && normalize_test;
}

// 测试图结构
bool TestGraph() {
    Graph graph = CreateSimpleGraph();
    
    // 测试节点数量
    bool node_count_test = graph.nodes().size() == 4;
    
    // 测试边数量
    bool edge_count_test = graph.edges().size() == 4;
    
    // 测试节点ID
    bool node_id_test = true;
    auto nodes = graph.nodes();
    for (size_t i = 0; i < nodes.size(); ++i) {
        std::string expected_id = "node" + std::to_string(i + 1);
        if (nodes[i]->id != expected_id) {
            node_id_test = false;
            break;
        }
    }
    
    return node_count_test && edge_count_test && node_id_test;
}

// 测试斥力模型
bool TestRepulsiveForce() {
    Graph graph = CreateSimpleGraph();
    RepulsiveForce repulsive_force;
    repulsive_force.set_k_repulsive(1000.0);
    
    // 记录初始位置
    auto nodes = graph.nodes();
    std::vector<Vector2D> initial_positions;
    for (const auto& node : nodes) {
        initial_positions.push_back(node->position);
        node->forces = Vector2D{0.0, 0.0}; // 重置力
    }
    
    // 应用斥力
    repulsive_force.Apply(graph);
    
    // 检查是否产生了斥力（力不为零）
    bool forces_applied = false;
    for (const auto& node : nodes) {
        if (node->forces.Magnitude() > 1e-6) {
            forces_applied = true;
            break;
        }
    }
    
    // 检查能量计算
    double energy = repulsive_force.Energy(graph);
    bool energy_positive = energy > 0;
    
    return forces_applied && energy_positive;
}

// 测试引力模型
bool TestAttractiveForce() {
    Graph graph = CreateSimpleGraph();
    AttractiveForce attractive_force;
    attractive_force.set_k_attractive(0.1);
    
    // 重置力
    auto nodes = graph.nodes();
    for (const auto& node : nodes) {
        node->forces = Vector2D{0.0, 0.0};
    }
    
    // 应用引力
    attractive_force.Apply(graph);
    
    // 检查是否产生了引力
    bool forces_applied = false;
    for (const auto& node : nodes) {
        if (node->forces.Magnitude() > 1e-6) {
            forces_applied = true;
            break;
        }
    }
    
    // 检查能量计算
    double energy = attractive_force.Energy(graph);
    bool energy_positive = energy >= 0; // 引力能量可能为0或正值
    
    return forces_applied && energy_positive;
}

// 测试配置结构
bool TestConfig() {
    Config config;
    
    // 测试默认值
    bool default_iterations = config.iterations == 800;
    bool default_damping = std::abs(config.damping - 0.9) < 1e-6;
    bool default_temperature = std::abs(config.temperature - 1.0) < 1e-6;
    
    // 测试修改配置
    config.iterations = 1000;
    config.damping = 0.8;
    bool modified_correctly = config.iterations == 1000 && std::abs(config.damping - 0.8) < 1e-6;
    
    return default_iterations && default_damping && default_temperature && modified_correctly;
}

// 测试ForceDirected主类
bool TestForceDirected() {
    Graph graph = CreateSimpleGraph();
    ForceDirected force_directed(graph);
    
    // 设置配置
    Config config;
    config.iterations = 10; // 少量迭代用于测试
    config.energy_threshold = 1e-6;
    force_directed.set_config(config);
    
    // 添加力模型
    auto repulsive = std::make_unique<RepulsiveForce>();
    repulsive->set_k_repulsive(1000.0);
    force_directed.AddForceModel(std::move(repulsive));
    
    auto attractive = std::make_unique<AttractiveForce>();
    attractive->set_k_attractive(0.1);
    force_directed.AddForceModel(std::move(attractive));
    
    // 记录初始位置
    auto nodes = graph.nodes();
    std::vector<Vector2D> initial_positions;
    for (const auto& node : nodes) {
        initial_positions.push_back(node->position);
    }
    
    // 运行算法
    force_directed.Run();
    
    // 检查位置是否发生变化
    auto final_graph = force_directed.graph();
    auto final_nodes = final_graph.nodes();
    bool positions_changed = false;
    
    for (size_t i = 0; i < final_nodes.size(); ++i) {
        Vector2D diff = final_nodes[i]->position - initial_positions[i];
        if (diff.Magnitude() > 1e-6) {
            positions_changed = true;
            break;
        }
    }
    
    return positions_changed;
}

// 性能测试
bool TestPerformance() {
    std::cout << "Running performance test..." << std::endl;
    
    // 创建较大的图进行性能测试
    Graph large_graph;
    const int num_nodes = 50;
    
    // 创建节点
    for (int i = 0; i < num_nodes; ++i) {
        auto node = std::make_shared<Node>();
        node->id = "node" + std::to_string(i);
        node->position = Vector2D{static_cast<double>(i * 10), static_cast<double>((i % 10) * 10)};
        node->width = 5.0;
        node->height = 5.0;
        large_graph.add_node(node);
    }
    
    // 创建边（每个节点连接到下一个节点）
    auto nodes = large_graph.nodes();
    for (int i = 0; i < num_nodes - 1; ++i) {
        auto edge = std::make_shared<Edge>();
        edge->id = "edge" + std::to_string(i);
        edge->source = nodes[i];
        edge->target = nodes[i + 1];
        large_graph.add_edge(edge);
    }
    
    ForceDirected force_directed(large_graph);
    Config config;
    config.iterations = 100;
    force_directed.set_config(config);
    
    auto repulsive = std::make_unique<RepulsiveForce>();
    auto attractive = std::make_unique<AttractiveForce>();
    force_directed.AddForceModel(std::move(repulsive));
    force_directed.AddForceModel(std::move(attractive));
    
    // 运行并测量时间
    auto start = std::chrono::high_resolution_clock::now();
    force_directed.Run();
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    std::cout << "Performance test completed in " << duration.count() << " ms" << std::endl;
    
    return duration.count() < 5000; // 应该在5秒内完成
}

int main() {
    std::cout << "=== Force Directed Layout Algorithm Test Suite ===" << std::endl;
    std::cout << std::endl;
    
    int passed_tests = 0;
    int total_tests = 0;
    
    // 运行所有测试
    std::vector<std::pair<std::string, bool>> test_results = {
        {"Vector2D Operations", TestVector2D()},
        {"Graph Structure", TestGraph()},
        {"Repulsive Force Model", TestRepulsiveForce()},
        {"Attractive Force Model", TestAttractiveForce()},
        {"Configuration", TestConfig()},
        {"ForceDirected Algorithm", TestForceDirected()}
    };
    
    for (const auto& result : test_results) {
        PrintTestResult(result.first, result.second);
        if (result.second) passed_tests++;
        total_tests++;
    }
    
    std::cout << std::endl;
    std::cout << "=== Test Summary ===" << std::endl;
    std::cout << "Passed: " << passed_tests << "/" << total_tests << std::endl;
    
    if (passed_tests == total_tests) {
        std::cout << "All tests passed! ✓" << std::endl;
        
        // 运行性能测试
        std::cout << std::endl;
        bool perf_result = TestPerformance();
        PrintTestResult("Performance Test", perf_result);
        
        return 0;
    } else {
        std::cout << "Some tests failed! ✗" << std::endl;
        return 1;
    }
}
