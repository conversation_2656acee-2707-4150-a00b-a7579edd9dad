#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include <cstdlib>
#include <random>
#include <iomanip>
#include <algorithm>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#include "force_directed.h"
#include "force_model.h"
#include "graph.h"
#include "vector2d.h"
#include "svg_visualizer.h"

using namespace force_directed;

// 随机数生成器
std::random_device rd;
std::mt19937 gen(42); // 固定种子以便重现结果

// 创建板框多边形
std::vector<Vector2D> CreateBoardBoundary(double width = 200.0, double height = 150.0) {
    std::vector<Vector2D> boundary;

    // 创建矩形板框
    boundary.push_back(Vector2D{-width/2, -height/2});  // 左下
    boundary.push_back(Vector2D{width/2, -height/2});   // 右下
    boundary.push_back(Vector2D{width/2, height/2});    // 右上
    boundary.push_back(Vector2D{-width/2, height/2});   // 左上

    return boundary;
}

// 创建圆角矩形板框
std::vector<Vector2D> CreateRoundedBoardBoundary(double width = 200.0, double height = 150.0, double corner_radius = 20.0) {
    std::vector<Vector2D> boundary;

    double half_w = width / 2.0;
    double half_h = height / 2.0;
    double r = corner_radius;

    // 从右下角开始，逆时针创建圆角矩形
    // 右下角
    boundary.push_back(Vector2D{half_w - r, -half_h});
    boundary.push_back(Vector2D{half_w, -half_h + r});

    // 右上角
    boundary.push_back(Vector2D{half_w, half_h - r});
    boundary.push_back(Vector2D{half_w - r, half_h});

    // 左上角
    boundary.push_back(Vector2D{-half_w + r, half_h});
    boundary.push_back(Vector2D{-half_w, half_h - r});

    // 左下角
    boundary.push_back(Vector2D{-half_w, -half_h + r});
    boundary.push_back(Vector2D{-half_w + r, -half_h});

    return boundary;
}

// 创建随机分布的测试图（带板框约束）
Graph CreateRandomGraphWithBoard(int num_nodes = 8, double board_width = 180.0, double board_height = 130.0) {
    Graph graph;

    std::cout << "Creating random graph with " << num_nodes << " nodes..." << std::endl;
    std::cout << "Board size: " << board_width << "x" << board_height << std::endl;

    // 在板框内随机分布，留出边距
    double margin = 20.0;
    std::uniform_real_distribution<double> pos_x_dist(-board_width/2 + margin, board_width/2 - margin);
    std::uniform_real_distribution<double> pos_y_dist(-board_height/2 + margin, board_height/2 - margin);
    std::uniform_real_distribution<double> size_dist(12.0, 25.0);
    std::uniform_int_distribution<int> type_dist(0, 1);
    
    std::vector<std::shared_ptr<Node>> nodes;
    
    // 创建节点
    for (int i = 0; i < num_nodes; ++i) {
        auto node = std::make_shared<Node>();
        node->id = "Node_" + std::to_string(i + 1);
        
        // 在板框内随机位置
        node->position = Vector2D{pos_x_dist(gen), pos_y_dist(gen)};
        
        // 随机尺寸
        node->width = size_dist(gen);
        node->height = size_dist(gen);
        
        // 随机类型
        node->type = (type_dist(gen) == 0) ? Node::Type::kRect : Node::Type::kCircle;
        
        nodes.push_back(node);
        graph.add_node(node);
        
        std::cout << "  " << node->id << " at (" 
                  << std::fixed << std::setprecision(1) 
                  << node->position.x() << ", " << node->position.y() 
                  << ") size: " << node->width << "x" << node->height
                  << " type: " << (node->type == Node::Type::kCircle ? "Circle" : "Rect")
                  << std::endl;
    }
    
    // 创建连接关系
    std::vector<std::pair<int, int>> connections;
    
    // 确保图是连通的 - 创建最小生成树
    for (int i = 1; i < num_nodes; ++i) {
        connections.push_back({i - 1, i});
    }
    
    // 添加一些额外的连接以增加复杂性
    std::uniform_int_distribution<int> node_dist(0, num_nodes - 1);
    int extra_edges = num_nodes / 2; // 额外边数
    
    for (int i = 0; i < extra_edges; ++i) {
        int from = node_dist(gen);
        int to = node_dist(gen);
        
        if (from != to) {
            // 检查是否已存在这条边
            bool exists = false;
            for (const auto& conn : connections) {
                if ((conn.first == from && conn.second == to) ||
                    (conn.first == to && conn.second == from)) {
                    exists = true;
                    break;
                }
            }
            
            if (!exists) {
                connections.push_back({from, to});
            }
        }
    }
    
    // 创建边
    std::cout << "\nCreating " << connections.size() << " connections:" << std::endl;
    for (size_t i = 0; i < connections.size(); ++i) {
        const auto& conn = connections[i];
        auto edge = std::make_shared<Edge>();
        edge->id = "Edge_" + std::to_string(i + 1);
        edge->source = nodes[conn.first];
        edge->target = nodes[conn.second];
        graph.add_edge(edge);
        
        std::cout << "  " << edge->source->id << " <-> " << edge->target->id << std::endl;
    }
    
    return graph;
}

// 创建密集重叠的测试图
Graph CreateDenseGraph(int num_nodes = 10) {
    Graph graph;
    
    std::cout << "Creating dense overlapping graph with " << num_nodes << " nodes..." << std::endl;
    
    std::uniform_real_distribution<double> pos_dist(-20.0, 20.0); // 小范围内随机分布
    std::uniform_real_distribution<double> size_dist(15.0, 30.0);
    
    std::vector<std::shared_ptr<Node>> nodes;
    
    // 创建节点
    for (int i = 0; i < num_nodes; ++i) {
        auto node = std::make_shared<Node>();
        node->id = "Comp_" + std::to_string(i + 1);
        
        // 在小范围内随机分布，容易重叠
        node->position = Vector2D{pos_dist(gen), pos_dist(gen)};
        node->width = size_dist(gen);
        node->height = size_dist(gen);
        node->type = (i % 3 == 0) ? Node::Type::kCircle : Node::Type::kRect;
        
        nodes.push_back(node);
        graph.add_node(node);
        
        std::cout << "  " << node->id << " at (" 
                  << std::fixed << std::setprecision(1)
                  << node->position.x() << ", " << node->position.y() 
                  << ") size: " << node->width << "x" << node->height << std::endl;
    }
    
    // 创建密集连接
    std::vector<std::pair<int, int>> connections;
    
    // 星形连接
    for (int i = 1; i < num_nodes; ++i) {
        connections.push_back({0, i});
    }
    
    // 环形连接
    for (int i = 1; i < num_nodes - 1; ++i) {
        connections.push_back({i, i + 1});
    }
    if (num_nodes > 2) {
        connections.push_back({num_nodes - 1, 1});
    }
    
    // 一些随机连接
    std::uniform_int_distribution<int> node_dist(0, num_nodes - 1);
    for (int i = 0; i < num_nodes / 2; ++i) {
        int from = node_dist(gen);
        int to = node_dist(gen);
        if (from != to) {
            connections.push_back({from, to});
        }
    }
    
    // 创建边
    std::cout << "\nCreating " << connections.size() << " connections..." << std::endl;
    for (size_t i = 0; i < connections.size(); ++i) {
        const auto& conn = connections[i];
        auto edge = std::make_shared<Edge>();
        edge->id = "Edge_" + std::to_string(i + 1);
        edge->source = nodes[conn.first];
        edge->target = nodes[conn.second];
        graph.add_edge(edge);
    }
    
    return graph;
}

// 运行可视化演示
void RunVisualizationDemo(int demo_type = 1) {
    std::cout << "=== Force Directed Layout Visualization Demo ===" << std::endl;

    // 创建图和板框
    Graph graph;
    std::vector<Vector2D> board_boundary;
    std::string demo_name;

    switch (demo_type) {
        case 1:
            graph = CreateRandomGraphWithBoard(8, 180.0, 130.0);
            board_boundary = CreateBoardBoundary(180.0, 130.0);
            demo_name = "random_board";
            break;
        case 2:
            graph = CreateDenseGraph(10);
            board_boundary = CreateRoundedBoardBoundary(160.0, 120.0, 15.0);
            demo_name = "dense_board";
            break;
        case 3:
            graph = CreateRandomGraphWithBoard(6, 200.0, 100.0);
            board_boundary = CreateBoardBoundary(200.0, 100.0);
            demo_name = "wide_board";
            break;
        default:
            graph = CreateRandomGraphWithBoard(6, 150.0, 150.0);
            board_boundary = CreateBoardBoundary(150.0, 150.0);
            demo_name = "square_board";
            break;
    }
    
    // 可视化初始状态
    std::cout << "\n=== Visualizing Initial State ===" << std::endl;
    std::string initial_file = "initial_" + demo_name + ".svg";
    visualize_graph_with_board(graph, board_boundary, initial_file);
    std::cout << "Initial layout with board saved to: " << initial_file << std::endl;
    
    // 配置算法
    std::cout << "\n=== Configuring Algorithm ===" << std::endl;
    ForceDirected force_directed(graph);
    
    Config config;
    config.iterations = 150;
    config.damping = 0.85;
    config.max_velocity = 25.0;
    config.temperature = 1.0;
    config.cooling_rate = 0.995;
    config.energy_threshold = 1e-4;
    
    force_directed.set_config(config);
    
    // 添加力模型
    auto repulsive = std::make_unique<RepulsiveForce>();
    repulsive->set_k_repulsive(2000.0);
    force_directed.AddForceModel(std::move(repulsive));

    auto attractive = std::make_unique<AttractiveForce>();
    attractive->set_k_attractive(0.1);
    force_directed.AddForceModel(std::move(attractive));

    // 添加板框约束力
    auto boundary_force = std::make_unique<BoundaryRepulsiveForce>();
    boundary_force->set_boundary_polygon(board_boundary);
    boundary_force->set_k_repulsive(3000.0);
    force_directed.AddForceModel(std::move(boundary_force));
    
    std::cout << "Algorithm configured:" << std::endl;
    std::cout << "  Iterations: " << config.iterations << std::endl;
    std::cout << "  Repulsive force: 2000.0" << std::endl;
    std::cout << "  Attractive force: 0.1" << std::endl;
    std::cout << "  Boundary force: 3000.0" << std::endl;
    
    // 运行算法
    std::cout << "\n=== Running Algorithm ===" << std::endl;
    auto start_time = std::chrono::high_resolution_clock::now();
    force_directed.Run();
    auto end_time = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "Algorithm completed in " << duration.count() << " ms" << std::endl;
    
    // 可视化最终状态
    std::cout << "\n=== Visualizing Final State ===" << std::endl;
    Graph final_graph = force_directed.graph();

    std::string final_file = "final_" + demo_name + ".svg";
    visualize_graph_with_board(final_graph, board_boundary, final_file);
    std::cout << "Final layout with board saved to: " << final_file << std::endl;

    // 可视化带力向量的状态
    std::string forces_file = "forces_" + demo_name + ".svg";
    SVGVisualizer force_visualizer;
    force_visualizer.config().show_forces = true;
    force_visualizer.config().force_scale = 0.2;
    force_visualizer.visualize_graph_with_board(final_graph, board_boundary, forces_file);
    std::cout << "Layout with forces and board saved to: " << forces_file << std::endl;
    
    // 创建自定义可视化
    std::cout << "\n=== Creating Custom Visualization ===" << std::endl;
    SVGVisualizer custom_visualizer;
    auto& custom_config = custom_visualizer.config();
    
    custom_config.canvas_width = 1000;
    custom_config.canvas_height = 800;
    custom_config.node_fill_color = Color{100, 200, 255, 0.8};
    custom_config.node_stroke_color = Color{50, 100, 200};
    custom_config.edge_color = Color{80, 80, 80};
    custom_config.edge_width = 2.0;
    custom_config.show_grid = true;
    custom_config.font_size = 14;
    
    std::string custom_file = "custom_" + demo_name + ".svg";
    custom_visualizer.visualize_graph_with_board(final_graph, board_boundary, custom_file);
    std::cout << "Custom visualization with board saved to: " << custom_file << std::endl;
    
    // 分析结果
    std::cout << "\n=== Analysis ===" << std::endl;
    auto initial_nodes = graph.nodes();
    auto final_nodes = final_graph.nodes();
    
    double total_movement = 0.0;
    double max_movement = 0.0;
    int overlap_count = 0;
    
    for (size_t i = 0; i < final_nodes.size(); ++i) {
        double movement = final_nodes[i]->position.DistanceTo(initial_nodes[i]->position);
        total_movement += movement;
        max_movement = std::max(max_movement, movement);
    }
    
    // 检查重叠
    for (size_t i = 0; i < final_nodes.size(); ++i) {
        for (size_t j = i + 1; j < final_nodes.size(); ++j) {
            const auto& node1 = final_nodes[i];
            const auto& node2 = final_nodes[j];
            
            double dx = std::abs(node1->position.x() - node2->position.x());
            double dy = std::abs(node1->position.y() - node2->position.y());
            double min_dx = (node1->width + node2->width) / 2.0;
            double min_dy = (node1->height + node2->height) / 2.0;
            
            if (dx < min_dx && dy < min_dy) {
                overlap_count++;
            }
        }
    }
    
    std::cout << "Layout Statistics:" << std::endl;
    std::cout << "  Nodes: " << final_nodes.size() << std::endl;
    std::cout << "  Edges: " << final_graph.edges().size() << std::endl;
    std::cout << "  Average movement: " << std::fixed << std::setprecision(2) 
              << (total_movement / final_nodes.size()) << " units" << std::endl;
    std::cout << "  Maximum movement: " << max_movement << " units" << std::endl;
    std::cout << "  Remaining overlaps: " << overlap_count << std::endl;
    std::cout << "  Processing time: " << duration.count() << " ms" << std::endl;
    
    std::cout << "\n=== Generated Files ===" << std::endl;
    std::cout << "  " << initial_file << " - Initial random layout with board boundary" << std::endl;
    std::cout << "  " << final_file << " - Optimized final layout with board boundary" << std::endl;
    std::cout << "  " << forces_file << " - Final layout with force vectors and board" << std::endl;
    std::cout << "  " << custom_file << " - Custom styled visualization with board" << std::endl;
}

int main(int argc, char* argv[]) {
    try {
        int demo_type = 1;
        
        if (argc > 1) {
            demo_type = std::atoi(argv[1]);
        }
        
        std::cout << "Available demo types:" << std::endl;
        std::cout << "  1 - Random layout with rectangular board (8 nodes)" << std::endl;
        std::cout << "  2 - Dense layout with rounded board (10 nodes)" << std::endl;
        std::cout << "  3 - Wide board layout (6 nodes)" << std::endl;
        std::cout << "Selected demo type: " << demo_type << std::endl;
        std::cout << std::endl;
        
        RunVisualizationDemo(demo_type);
        
        std::cout << "\n=== Demo Complete ===" << std::endl;
        std::cout << "Open the generated .svg files in a web browser to view results!" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
