#include "svg_visualizer.h"
#include <iostream>
#include <iomanip>

namespace force_directed {

SVGVisualizer::SVGVisualizer(const VisualizationConfig& config) : config_(config) {}

void SVGVisualizer::visualize_graph(const Graph& graph, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open file " << filename << " for writing" << std::endl;
        return;
    }
    
    // 计算边界框
    BoundingBox bbox = calculate_bounding_box(graph);
    if (bbox.width() <= 0 || bbox.height() <= 0) {
        std::cerr << "Warning: Empty or invalid graph bounds" << std::endl;
        return;
    }
    
    // 计算变换参数
    auto [scale, offset_x, offset_y] = calculate_transform(bbox);
    
    // 写入SVG
    write_svg_header(file, bbox);
    
    if (config_.show_grid) {
        draw_grid(file, bbox, scale, offset_x, offset_y);
    }
    
    if (config_.show_edges) {
        draw_edges(file, graph, scale, offset_x, offset_y);
    }
    
    if (config_.show_forces) {
        draw_forces(file, graph, scale, offset_x, offset_y);
    }
    
    if (config_.show_nodes) {
        draw_nodes(file, graph, scale, offset_x, offset_y);
    }
    
    if (config_.show_node_labels) {
        draw_labels(file, graph, scale, offset_x, offset_y);
    }
    
    write_svg_footer(file);
    file.close();
    
    std::cout << "Graph visualization saved to: " << filename << std::endl;
}

BoundingBox SVGVisualizer::calculate_bounding_box(const Graph& graph) const {
    BoundingBox bbox;
    
    for (const auto& node : graph.nodes()) {
        // 考虑节点的位置和尺寸
        double half_width = node->width / 2.0;
        double half_height = node->height / 2.0;
        
        bbox.update(node->position.x() - half_width, node->position.y() - half_height);
        bbox.update(node->position.x() + half_width, node->position.y() + half_height);
    }
    
    // 添加一些边距
    double margin = std::max(bbox.width(), bbox.height()) * 0.1;
    bbox.min_x -= margin;
    bbox.min_y -= margin;
    bbox.max_x += margin;
    bbox.max_y += margin;
    
    return bbox;
}

std::pair<double, double> SVGVisualizer::calculate_transform(const BoundingBox& bbox) const {
    double available_width = config_.canvas_width - 2 * config_.margin;
    double available_height = config_.canvas_height - 2 * config_.margin;
    
    double scale_x = available_width / bbox.width();
    double scale_y = available_height / bbox.height();
    double scale = std::min(scale_x, scale_y);
    
    // 计算偏移以居中
    double scaled_width = bbox.width() * scale;
    double scaled_height = bbox.height() * scale;
    
    double offset_x = config_.margin + (available_width - scaled_width) / 2.0 - bbox.min_x * scale;
    double offset_y = config_.margin + (available_height - scaled_height) / 2.0 - bbox.min_y * scale;
    
    return {scale, offset_x, offset_y};
}

void SVGVisualizer::write_svg_header(std::ofstream& file, const BoundingBox& bbox) const {
    file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    file << "<svg width=\"" << config_.canvas_width << "\" height=\"" << config_.canvas_height 
         << "\" xmlns=\"http://www.w3.org/2000/svg\">\n";
    file << "<defs>\n";
    file << "  <style>\n";
    file << "    .node { cursor: pointer; }\n";
    file << "    .node:hover { opacity: 0.8; }\n";
    file << "    .edge { pointer-events: none; }\n";
    file << "    .force-vector { pointer-events: none; }\n";
    file << "    .label { pointer-events: none; font-family: Arial, sans-serif; }\n";
    file << "  </style>\n";
    file << "</defs>\n";
    
    // 背景
    file << "<rect width=\"100%\" height=\"100%\" fill=\"white\"/>\n";
    
    // 标题
    file << "<text x=\"" << config_.canvas_width / 2 << "\" y=\"25\" "
         << "text-anchor=\"middle\" font-size=\"16\" font-weight=\"bold\" fill=\"black\">"
         << "Force Directed Layout Visualization</text>\n";
}

void SVGVisualizer::write_svg_footer(std::ofstream& file) const {
    file << "</svg>\n";
}

void SVGVisualizer::draw_grid(std::ofstream& file, const BoundingBox& bbox, 
                             double scale, double offset_x, double offset_y) const {
    double grid_size = 50.0; // 网格大小
    Color grid_color{200, 200, 200, 0.5};
    
    file << "<g class=\"grid\">\n";
    
    // 垂直线
    for (double x = std::floor(bbox.min_x / grid_size) * grid_size; x <= bbox.max_x; x += grid_size) {
        double svg_x = transform_x(x, scale, offset_x);
        file << "<line x1=\"" << svg_x << "\" y1=\"" << config_.margin 
             << "\" x2=\"" << svg_x << "\" y2=\"" << (config_.canvas_height - config_.margin)
             << "\" stroke=\"" << grid_color.to_string() << "\" stroke-width=\"0.5\"/>\n";
    }
    
    // 水平线
    for (double y = std::floor(bbox.min_y / grid_size) * grid_size; y <= bbox.max_y; y += grid_size) {
        double svg_y = transform_y(y, scale, offset_y);
        file << "<line x1=\"" << config_.margin << "\" y1=\"" << svg_y 
             << "\" x2=\"" << (config_.canvas_width - config_.margin) << "\" y2=\"" << svg_y
             << "\" stroke=\"" << grid_color.to_string() << "\" stroke-width=\"0.5\"/>\n";
    }
    
    file << "</g>\n";
}

void SVGVisualizer::draw_edges(std::ofstream& file, const Graph& graph,
                              double scale, double offset_x, double offset_y) const {
    file << "<g class=\"edges\">\n";
    
    for (const auto& edge : graph.edges()) {
        if (!edge->source || !edge->target) continue;
        
        double x1 = transform_x(edge->source->position.x(), scale, offset_x);
        double y1 = transform_y(edge->source->position.y(), scale, offset_y);
        double x2 = transform_x(edge->target->position.x(), scale, offset_x);
        double y2 = transform_y(edge->target->position.y(), scale, offset_y);
        
        file << "<line class=\"edge\" x1=\"" << x1 << "\" y1=\"" << y1 
             << "\" x2=\"" << x2 << "\" y2=\"" << y2 
             << "\" stroke=\"" << config_.edge_color.to_string() 
             << "\" stroke-width=\"" << config_.edge_width << "\"/>\n";
    }
    
    file << "</g>\n";
}

void SVGVisualizer::draw_nodes(std::ofstream& file, const Graph& graph,
                              double scale, double offset_x, double offset_y) const {
    file << "<g class=\"nodes\">\n";
    
    for (const auto& node : graph.nodes()) {
        double cx = transform_x(node->position.x(), scale, offset_x);
        double cy = transform_y(node->position.y(), scale, offset_y);
        double width = node->width * scale;
        double height = node->height * scale;
        
        if (node->type == Node::Type::kCircle) {
            double radius = std::max(width, height) / 2.0;
            file << "<circle class=\"node\" cx=\"" << cx << "\" cy=\"" << cy 
                 << "\" r=\"" << radius 
                 << "\" fill=\"" << config_.node_fill_color.to_string()
                 << "\" stroke=\"" << config_.node_stroke_color.to_string()
                 << "\" stroke-width=\"" << config_.node_stroke_width << "\"/>\n";
        } else {
            double x = cx - width / 2.0;
            double y = cy - height / 2.0;
            file << "<rect class=\"node\" x=\"" << x << "\" y=\"" << y 
                 << "\" width=\"" << width << "\" height=\"" << height
                 << "\" fill=\"" << config_.node_fill_color.to_string()
                 << "\" stroke=\"" << config_.node_stroke_color.to_string()
                 << "\" stroke-width=\"" << config_.node_stroke_width << "\"/>\n";
        }
    }
    
    file << "</g>\n";
}

void SVGVisualizer::draw_forces(std::ofstream& file, const Graph& graph,
                               double scale, double offset_x, double offset_y) const {
    // 添加箭头标记定义
    file << "<defs>\n";
    file << "<marker id=\"arrowhead\" markerWidth=\"10\" markerHeight=\"7\" "
         << "refX=\"9\" refY=\"3.5\" orient=\"auto\">\n";
    file << "<polygon points=\"0 0, 10 3.5, 0 7\" fill=\""
         << config_.force_color.to_string() << "\"/>\n";
    file << "</marker>\n";
    file << "</defs>\n";

    file << "<g class=\"force-vectors\">\n";

    for (const auto& node : graph.nodes()) {
        if (node->forces.Magnitude() < 1e-6) continue; // 跳过很小的力

        double start_x = transform_x(node->position.x(), scale, offset_x);
        double start_y = transform_y(node->position.y(), scale, offset_y);

        Vector2D scaled_force = node->forces * config_.force_scale * scale;
        double end_x = start_x + scaled_force.x();
        double end_y = start_y - scaled_force.y(); // SVG y轴向下

        // 绘制力向量箭头
        file << "<line class=\"force-vector\" x1=\"" << start_x << "\" y1=\"" << start_y
             << "\" x2=\"" << end_x << "\" y2=\"" << end_y
             << "\" stroke=\"" << config_.force_color.to_string()
             << "\" stroke-width=\"" << config_.force_width
             << "\" marker-end=\"url(#arrowhead)\"/>\n";
    }

    file << "</g>\n";
}

void SVGVisualizer::draw_labels(std::ofstream& file, const Graph& graph,
                               double scale, double offset_x, double offset_y) const {
    file << "<g class=\"labels\">\n";
    
    for (const auto& node : graph.nodes()) {
        double cx = transform_x(node->position.x(), scale, offset_x);
        double cy = transform_y(node->position.y(), scale, offset_y);
        
        file << "<text class=\"label\" x=\"" << cx << "\" y=\"" << (cy + config_.font_size / 3)
             << "\" text-anchor=\"middle\" font-size=\"" << config_.font_size 
             << "\" fill=\"" << config_.text_color.to_string() << "\">"
             << escape_xml(node->id) << "</text>\n";
    }
    
    file << "</g>\n";
}

double SVGVisualizer::transform_x(double x, double scale, double offset_x) const {
    return x * scale + offset_x;
}

double SVGVisualizer::transform_y(double y, double scale, double offset_y) const {
    return y * scale + offset_y;
}

std::string SVGVisualizer::escape_xml(const std::string& text) const {
    std::string result = text;
    size_t pos = 0;
    while ((pos = result.find('&', pos)) != std::string::npos) {
        result.replace(pos, 1, "&amp;");
        pos += 5;
    }
    pos = 0;
    while ((pos = result.find('<', pos)) != std::string::npos) {
        result.replace(pos, 1, "&lt;");
        pos += 4;
    }
    pos = 0;
    while ((pos = result.find('>', pos)) != std::string::npos) {
        result.replace(pos, 1, "&gt;");
        pos += 4;
    }
    return result;
}

void SVGVisualizer::visualize_layout_process(const std::vector<Graph>& snapshots,
                                            const std::string& base_filename) {
    if (snapshots.empty()) return;

    std::vector<std::string> svg_files;

    for (size_t i = 0; i < snapshots.size(); ++i) {
        std::string filename = base_filename + "_frame_" + std::to_string(i) + ".svg";
        visualize_graph(snapshots[i], filename);
        svg_files.push_back(filename);
    }

    // 创建HTML动画
    std::string html_filename = base_filename + "_animation.html";
    create_animation(svg_files, html_filename);
}

void SVGVisualizer::create_animation(const std::vector<std::string>& svg_files,
                                   const std::string& output_html,
                                   double frame_duration) {
    std::ofstream file(output_html);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot create animation file " << output_html << std::endl;
        return;
    }

    file << "<!DOCTYPE html>\n<html>\n<head>\n";
    file << "<title>Force Directed Layout Animation</title>\n";
    file << "<style>\n";
    file << "body { font-family: Arial, sans-serif; text-align: center; margin: 20px; }\n";
    file << "#animation-container { margin: 20px auto; }\n";
    file << ".controls { margin: 20px; }\n";
    file << "button { margin: 5px; padding: 10px 20px; font-size: 14px; }\n";
    file << "#frame-info { margin: 10px; font-size: 16px; }\n";
    file << "</style>\n</head>\n<body>\n";

    file << "<h1>Force Directed Layout Animation</h1>\n";
    file << "<div class=\"controls\">\n";
    file << "<button onclick=\"playAnimation()\">Play</button>\n";
    file << "<button onclick=\"pauseAnimation()\">Pause</button>\n";
    file << "<button onclick=\"resetAnimation()\">Reset</button>\n";
    file << "<button onclick=\"stepForward()\">Step Forward</button>\n";
    file << "<button onclick=\"stepBackward()\">Step Backward</button>\n";
    file << "</div>\n";

    file << "<div id=\"frame-info\">Frame: <span id=\"current-frame\">0</span> / "
         << (svg_files.size() - 1) << "</div>\n";

    file << "<div id=\"animation-container\">\n";
    for (size_t i = 0; i < svg_files.size(); ++i) {
        file << "<div id=\"frame-" << i << "\" style=\"display: "
             << (i == 0 ? "block" : "none") << ";\">\n";
        file << "<object data=\"" << svg_files[i] << "\" type=\"image/svg+xml\"></object>\n";
        file << "</div>\n";
    }
    file << "</div>\n";

    // JavaScript动画控制
    file << "<script>\n";
    file << "let currentFrame = 0;\n";
    file << "let totalFrames = " << svg_files.size() << ";\n";
    file << "let isPlaying = false;\n";
    file << "let animationInterval;\n\n";

    file << "function showFrame(frameIndex) {\n";
    file << "  for (let i = 0; i < totalFrames; i++) {\n";
    file << "    document.getElementById('frame-' + i).style.display = 'none';\n";
    file << "  }\n";
    file << "  document.getElementById('frame-' + frameIndex).style.display = 'block';\n";
    file << "  document.getElementById('current-frame').textContent = frameIndex;\n";
    file << "}\n\n";

    file << "function playAnimation() {\n";
    file << "  if (!isPlaying) {\n";
    file << "    isPlaying = true;\n";
    file << "    animationInterval = setInterval(() => {\n";
    file << "      currentFrame = (currentFrame + 1) % totalFrames;\n";
    file << "      showFrame(currentFrame);\n";
    file << "    }, " << frame_duration << ");\n";
    file << "  }\n";
    file << "}\n\n";

    file << "function pauseAnimation() {\n";
    file << "  isPlaying = false;\n";
    file << "  clearInterval(animationInterval);\n";
    file << "}\n\n";

    file << "function resetAnimation() {\n";
    file << "  pauseAnimation();\n";
    file << "  currentFrame = 0;\n";
    file << "  showFrame(currentFrame);\n";
    file << "}\n\n";

    file << "function stepForward() {\n";
    file << "  pauseAnimation();\n";
    file << "  currentFrame = (currentFrame + 1) % totalFrames;\n";
    file << "  showFrame(currentFrame);\n";
    file << "}\n\n";

    file << "function stepBackward() {\n";
    file << "  pauseAnimation();\n";
    file << "  currentFrame = (currentFrame - 1 + totalFrames) % totalFrames;\n";
    file << "  showFrame(currentFrame);\n";
    file << "}\n";

    file << "</script>\n</body>\n</html>\n";
    file.close();

    std::cout << "Animation created: " << output_html << std::endl;
}

// 便利函数实现
void visualize_graph_simple(const Graph& graph, const std::string& filename) {
    SVGVisualizer visualizer;
    visualizer.visualize_graph(graph, filename);
}

void visualize_with_forces(const Graph& graph, const std::string& filename) {
    SVGVisualizer visualizer;
    visualizer.config().show_forces = true;
    visualizer.config().force_scale = 0.2;
    visualizer.visualize_graph(graph, filename);
}

} // namespace force_directed
