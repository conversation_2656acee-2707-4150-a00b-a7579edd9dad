#ifndef GRAPH_H
#define GRAPH_H

#include <memory>
#include <string>
#include <vector>

#include "vector2d.h"

namespace force_directed {
struct Node {
  enum class Type { kCircle, kRect };
  std::string id;

  double width{0.0};
  double height{0.0};
  bool is_fixed{false};
  Type type {Type::kRect};

  Vector2D forces;
  Vector2D position;
  Vector2D velocity;
};

struct Edge {
  std::string id;
  std::shared_ptr<Node> source;
  std::shared_ptr<Node> target;
};

class Graph {
  using Nodes = std::vector<std::shared_ptr<Node>>;
  using Edges = std::vector<std::shared_ptr<Edge>>;

 public:
  Nodes nodes() const { return nodes_; }
  Edges edges() const { return edges_; }

  void set_nodes(const Nodes& nodes) { nodes_ = nodes; }
  void set_edges(const Edges& edges) { edges_ = edges; }
  void add_node(const std::shared_ptr<Node>& node) { nodes_.push_back(node); }
  void add_edge(const std::shared_ptr<Edge>& edge) { edges_.push_back(edge); }

 private:
  Nodes nodes_;
  Edges edges_;
};

}  // namespace force_directed

#endif  // GRAPH_H
