#include "force_model.h"

#include <algorithm>
#include <cmath>

#include "polygon_utils.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace force_directed {

constexpr double kThreshold = 1e-5;

// todo: optimize: this function is calling frequently.
static std::vector<Vector2D> InPolygonPoints(
    const std::vector<Vector2D>& points,
    const Polygon& polygon) {
  std::vector<Vector2D> in_board_points;
  in_board_points.reserve(points.size());

  for (const auto& point : points) {
    if (is_point_in_polygon(point, polygon)) {
      in_board_points.push_back(point);
    }
  }
  return in_board_points;
}

// 排斥力 - 库仑定律(F = k * q1 * q2 / r^2)
void RepulsiveForce::Apply(Graph& graph) {
  auto nodes = graph.nodes();
  for (int i = 0; i < nodes.size(); ++i) {
    for (int j = i + 1; j < nodes.size(); ++j) {
      const auto& node1 = nodes[i];
      const auto& node2 = nodes[j];
      auto vector = node1->position - node2->position;
      auto distance = vector.Magnitude();

      // 处理完全重叠的情况
      if (distance < kThreshold) {
        // 为完全重叠的节点生成随机分离方向
        double angle = 2.0 * M_PI * (i * 7 + j * 13) / 100.0; // 伪随机角度
        vector = Vector2D{std::cos(angle), std::sin(angle)} * kThreshold;
        distance = kThreshold;
      }

      // check overlap
      const double dx = std::abs(node1->position.x() - node2->position.x());
      const double dy = std::abs(node1->position.y() - node2->position.y());
      const double overlap_x = (node1->width + node2->width) / 2.0 - dx;
      const double overlap_y = (node1->height + node2->height) / 2.0 - dy;
      Vector2D force{0.0, 0.0};

      if (overlap_x > 0 && overlap_y > 0) {
        // 节点重叠情况
        const double overlap_area = overlap_x * overlap_y;
        const double force_magnitude =
            k_repulsive_ * (5.0 + overlap_area / (node1->width * node1->height));

        // 安全的归一化
        if (distance > kThreshold) {
          vector = vector / distance;
        }
        force = force_magnitude * vector;
      } else {
        // 节点接近但不重叠的情况
        const double min_distance = (node1->width + node2->width) / 2.0 +
                                   (node1->height + node2->height) / 2.0 + 5.0; // 添加间隙
        if (distance < min_distance) {
          const double safe_distance = std::max(distance, kThreshold);
          const double force_magnitude =
              k_repulsive_ * (1.0 / (safe_distance * safe_distance + kThreshold)) *
              ((min_distance / safe_distance) - 0.5);

          // 安全的归一化
          if (distance > kThreshold) {
            vector = vector / distance;
          }
          force = force_magnitude * vector;
        }
      }

      node1->forces += force;
      node2->forces -= force;
    }
  }
}

double RepulsiveForce::Energy(const Graph& graph) {
  double energy = 0.0;
  for (int i = 0; i < graph.nodes().size(); ++i) {
    for (int j = i + 1; j < graph.nodes().size(); ++j) {
      const auto node1 = graph.nodes()[i];
      const auto node2 = graph.nodes()[j];
      double distance = node1->position.DistanceTo(node2->position);
      double min_distance = (node1->width + node2->width) / 2.0 +
                            (node1->height + node2->height) / 2.0;
      if (distance < min_distance) {
        energy += k_repulsive_ * (1.0 / (distance + kThreshold));
      }
    }
  }
  return energy;
}

// 引力 - 胡克定律(F = -k * (d - L0))
void AttractiveForce::Apply(Graph& graph) {
  auto edges = graph.edges();
  for (const auto& edge : edges) {
    const auto& node1 = edge->source;
    const auto& node2 = edge->target;
    auto vector = node1->position - node2->position;
    const auto distance = vector.Magnitude();
    if (distance < kThreshold) {
      vector = Vector2D{kThreshold, 0.0};
    }
    const double ideal_length =
        std::sqrt(node1->height / 2.0 * node1->width / 2.0) +
        std::sqrt(node2->height / 2.0 * node2->width / 2.0);

    // todo: need check force direction is correct
    const auto force_magnitude =
        -k_attractive_ * (distance - ideal_length) * 1.0;  // weight=1.0
    const auto force = force_magnitude * (vector / distance);
    node1->forces += force;
    node2->forces -= force;
  }
}

double AttractiveForce::Energy(const Graph& graph) {
  double energy = 0.0;
  auto edges  = graph.edges();
  for (const auto& edge: edges) {
    auto node1 = edge->source;
    auto node2 = edge->target;
    double distance = node1->position.DistanceTo(node2->position);
    double ideal_length =
        std::sqrt(node1->height / 2.0 * node1->width / 2.0) +
        std::sqrt(node2->height / 2.0 * node2->width / 2.0);
    energy += 0.5 * k_attractive_ * (distance - ideal_length) * (distance - ideal_length) * 1.0;  // weight=1.0
  }
  return energy;
}

void BoundaryRepulsiveForce::Apply(Graph& graph) {
  for (const auto& node : graph.nodes()) {
    auto comp_polygon = std::vector<Vector2D>{
        {node->position.x() - node->width / 2.0,
         node->position.y() - node->height / 2.0},
        {node->position.x() + node->width / 2.0,
         node->position.y() - node->height / 2.0},
        {node->position.x() + node->width / 2.0,
         node->position.y() + node->height / 2.0},
        {node->position.x() - node->width / 2.0,
         node->position.y() + node->height / 2.0},
    };

    auto in_board_points = InPolygonPoints(comp_polygon, boundary_polygon_);

    if (in_board_points.empty()) {
      // comp out of board.
      auto ret = distance_to_polygon(node->position, boundary_polygon_);
      const auto center_dist = std::get<0>(ret);
      auto direction = std::get<2>(ret);
      const auto force_magnitude =
          k_repulsive_ * 5.0 * (1.0 + center_dist / 100.0);
      node->forces += force_magnitude * (-direction);
    } else if (in_board_points.size() != comp_polygon.size()) {
      // comp partly in board.
      std::for_each(
          comp_polygon.begin(), comp_polygon.end(), [&](const Point& point) {
            if (!is_point_in_polygon(point, boundary_polygon_)) {
              auto ret = distance_to_polygon(point, boundary_polygon_);
              const auto dist = std::get<0>(ret);
              auto direction = std::get<2>(ret);
              const auto force_magnitude =
                  k_repulsive_ * 2.0 / (dist + kThreshold);
              node->forces += force_magnitude * (-direction);
            }
          });
    } else {
      // comp in board.
      auto ret = distance_to_polygon(node->position, boundary_polygon_);
      const double board_margin = std::min(node->width, node->height) * 0.5;
      const auto center_dist = std::get<0>(ret);
      auto direction = std::get<2>(ret);
      if (center_dist < board_margin) {
        const auto force_magnitude =
            k_repulsive_ * 0.1 * (1.0 - center_dist / board_margin);
        node->forces += force_magnitude * direction;
      }
    }
  }
}

double BoundaryRepulsiveForce::Energy(const Graph& graph) {
  return 0.0;
}

void KeepoutRepulsiveForce::Apply(Graph& graph) {
  for (const auto& node : graph.nodes()) {
    auto comp_polygon = std::vector<Vector2D>{
        {node->position.x() - node->width / 2.0, node->position.y() - node->height / 2.0},
        {node->position.x() + node->width / 2.0,  node->position.y() - node->height / 2.0},
        {node->position.x() + node->width / 2.0, node->position.y() + node->height / 2.0},
        {node->position.x() - node->width / 2.0, node->position.y() + node->height / 2.0},
    };

    for (const auto& keepout_polygon : keepout_polygons_) {
      if (polygons_intersect(keepout_polygon, comp_polygon)) {
        auto ret = distance_to_polygon(node->position, keepout_polygon);
        if (is_point_in_polygon(node->position, keepout_polygon)) {
          auto direction = std::get<2>(ret);
          const auto force_magnitude = k_repulsive_ * 5.0;
          node->forces += force_magnitude * (-direction);
        } else {
          const auto dist = std::get<0>(ret);
          auto direction = std::get<2>(ret);
          const auto force_magnitude = k_repulsive_ * 2.0 / (dist + kThreshold);
          node->forces += force_magnitude * direction;
        }
      }else {
        const double min_dist = min_distance_to_polygon(comp_polygon, keepout_polygon);
        const double safety_margin = std::max(node->width, node->height) * 0.3;
        if (min_dist < safety_margin) {
          auto ret = distance_to_polygon(node->position, keepout_polygon);
          auto direction = std::get<2>(ret);
          const auto force_magnitude = k_repulsive_ * 0.5 * (1.0 - min_dist / safety_margin);
          node->forces += force_magnitude * direction;
        }
      }
    }
  }
}
double KeepoutRepulsiveForce::Energy(const Graph& graph) {
  return 0.0;
}

// keep topology layout
void TopologyForce::Apply(Graph& graph) {
  Vector2D current_center;
  for (const auto& node : graph.nodes()) {
    current_center += node->position;
  }
  current_center = current_center / static_cast<double>(graph.nodes().size());
  for (const auto& node : graph.nodes()) {
    auto expected_position = current_center + initial_relative_pos_[node->id];
    node->forces += (expected_position - node->position) * 5;
  }
}

double TopologyForce::Energy(const Graph& graph) {
  return 0.0;
}

}  // namespace force_directed
