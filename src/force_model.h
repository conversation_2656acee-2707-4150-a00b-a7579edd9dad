#ifndef FORCELAYOUT_FORCE_MODEL_H
#define FORCELAYOUT_FORCE_MODEL_H
#include <unordered_map>

#include "graph.h"
#include "polygon_utils.h"

namespace force_directed {
/**
 * @brief Base class for all force models
 * Defines the  interface for force models in force-directed layout algorithms.
 */
class ForceModel {
public:
  /**
   * @brief  Apply force to the graph
   * @param graph The graph object to apply forces to
   *
   * Pure virtual function, implemented by subclasses for specific force calculation and application
   */
  virtual void Apply(Graph& graph) = 0;

  /**
   * @brief Calculate the energy of current force model
   * @return Returns the total energy value of current force model
   *
   * Used to evaluate system stability and convergence state
   */
  virtual double Energy(const Graph& graph) { return 0.0;}
  virtual ~ForceModel() = default;
};

/**
 * @brief Repulsive Force Model
 *
 * Implements repulsive forces between nodes to prevent overlapping.
 * Based on <PERSON>ulomb's law or similar inverse square law.
 */
class RepulsiveForce final : public ForceModel {
public:
  void Apply(Graph& graph) override;
  double Energy(const Graph& graph) override ;

  //setter
  void set_k_repulsive(double k) {
    k_repulsive_ = k;
  }
private:
  double k_repulsive_{5e6};
};

  /**
   * @brief Attractive Force Model
   *
   * Implements attractive forces between connected nodes to bring adjacent nodes closer.
   * Based on Hooke's law (spring force).
   */
  class AttractiveForce final : public ForceModel {
  public:
    void Apply(Graph& graph) override;
    double Energy(const Graph& graph) override;

    //setter
    void set_k_attractive(double k) {
      k_attractive_ = k;
    }
  private:
    double k_attractive_ {0.5};
  };

  /**
   * @brief Boundary Repulsive Force Model
   *
   * Implements repulsive forces from canvas boundaries to keep nodes within specified area.
   */
  class BoundaryRepulsiveForce final : public ForceModel {
  public:
    void Apply(Graph& graph) override;
    double Energy(const Graph& graph) override;

    //setter
    void set_boundary_polygon(const std::vector<Vector2D>& boundary) {
      boundary_polygon_ = boundary;
    }
    void set_k_repulsive(double k) {
      k_repulsive_ = k;
    }
  private:
    Polygon boundary_polygon_;
    double k_repulsive_{5e6};
  };

  /**
   * @brief Keepout Area Repulsive Force Model
   *
   * Implements repulsive forces from keepout areas to prevent nodes from entering specific regions.
   * Used for handling obstacles or forbidden areas in layout.
   */
  class KeepoutRepulsiveForce final : public ForceModel {
  public:
    void Apply(Graph& graph) override;
    double Energy(const Graph& graph) override;
    void  set_keepout_polygons(const std::vector<std::vector<Vector2D>>& polygons) {
      keepout_polygons_ = polygons;
    }
  private:
    std::vector<std::vector<Vector2D>> keepout_polygons_;
    double k_repulsive_{5e6};
  };

  /**
   * @brief Topology Preservation Force Model
   *
   * Implements forces to preserve topological relationships of the graph,
   * ensuring original structural characteristics are maintained during layout.
   */
  class TopologyForce final : public ForceModel {
  public:
    void Apply(Graph& graph) override;
    double Energy(const Graph& graph) override;
    //setter
    void set_initial_relative_pos(const std::unordered_map<std::string,Vector2D>& pos) {
      initial_relative_pos_ = pos;
    }
  private:
    std::unordered_map<std::string,Vector2D> initial_relative_pos_;
  };

}  // namespace force_directed
#endif  // FORCELAYOUT_FORCE_MODEL_H
