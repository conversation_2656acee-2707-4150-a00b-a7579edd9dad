#include "polygon_utils.h"

#include <algorithm>
#include <cmath>
#include <limits>
namespace force_directed {
bool is_point_in_polygon(const Point& point, const Polygon& polygon) {
  double x = point.x();
  double y = point.y();
  auto  n = polygon.size();
  bool inside = false;

  double p1x = polygon[0].x();
  double p1y = polygon[0].y();

  for (int i = 0; i < n + 1; ++i) {
    double p2x = polygon[i % n].x();
    double p2y = polygon[i % n].y();

    if (y > std::min(p1y, p2y)) {
      if (y <= std::max(p1y, p2y)) {
        if (x <= std::max(p1x, p2x)) {
          double x_intersect{};
          if (p1y != p2y) {
            x_intersect = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x;
          }
          if (p1x == p2x || x <= x_intersect) {
            inside = !inside;
          }
        }
      }
    }
    p1x = p2x;
    p1y = p2y;
  }

  return inside;
}

std::pair<double, Point> distance_to_segment(const Point& point, const std::pair<Point, Point>& segment) {
  const Point& p = segment.first;
  const Point& q = segment.second;
  double px = p.x(), py = p.y();
  double qx = q.x(), qy = q.y();

  // 向量PQ
  double dx = qx - px;
  double dy = qy - py;

  // 如果线段是一个点
  if (dx == 0 && dy == 0) {
    double dist = std::sqrt((px - point.x()) * (px - point.x()) +
                           (py - point.y()) * (py - point.y()));
    return {dist, {px, py}};
  }

  // 点在线段上的投影参数t
  double t = ((point.x() - px) * dx + (point.y() - py) * dy) / (dx * dx + dy * dy);

  Point closest_point;
  if (t < 0) {
    // 最近点是P
    closest_point = {px, py};
  } else if (t > 1) {
    // 最近点是Q
    closest_point = {qx, qy};
  } else {
    // 最近点在线段PQ上
    closest_point = {px + t * dx, py + t * dy};
  }

  // 计算距离
  double dist = std::sqrt((point.x() - closest_point.x()) * (point.x() - closest_point.x()) +
                         (point.y() - closest_point.y()) * (point.y() - closest_point.y()));
  return {dist, closest_point};
}

std::tuple<double, Point, Vector2D> distance_to_polygon(const Point& point, const Polygon& polygon) {
  if (polygon.empty()) {
    throw std::invalid_argument("多边形不能为空");
  }

  double min_dist = std::numeric_limits<double>::infinity();
  Point closest_point;

  auto  n = polygon.size();
  for (int i = 0; i < n; ++i) {
    std::pair<Point, Point> segment = {polygon[i], polygon[(i + 1) % n]};
    auto pair = distance_to_segment(point, segment);
    auto dist = pair.first;
    auto point_on_segment = pair.second;
    if (dist < min_dist) {
      min_dist = dist;
      closest_point = point_on_segment;
    }
  }

  // 计算方向向量 (从最近点指向原始点)
  Vector2D direction(point.x() - closest_point.x(), point.y() - closest_point.y());

  // 归一化方向向量
  double direction_norm = direction.Magnitude();
  if (direction_norm > 0) {
    direction = direction / direction_norm;
  } else {
    // 处理极少数情况：方向向量为零
    direction = Vector2D(0.0, 0.0);
  }

  return {min_dist, closest_point, direction};
}

Point calculate_polygon_centroid(const Polygon& polygon) {
  auto  n = polygon.size();
  double area = 0.0;
  double cx = 0.0;
  double cy = 0.0;

  for (int i = 0; i < n; ++i) {
    int j = (i + 1) % n;
    double cross = polygon[i].x() * polygon[j].y() - polygon[j].x() * polygon[i].y();
    area += cross;
    cx += (polygon[i].x() + polygon[j].x()) * cross;
    cy += (polygon[i].y() + polygon[j].y()) * cross;
  }

  area *= 0.5;
  if (std::abs(area) < 1e-10) {  // 避免除零
    // 如果面积为0，返回几何中心
    cx = 0.0;
    cy = 0.0;
    for (const auto& p : polygon) {
      cx += p.x();
      cy += p.y();
    }
    cx /= n;
    cy /= n;
  } else {
    cx /= 6.0 * area;
    cy /= 6.0 * area;
  }

  return {cx, cy};
}

// 计算三个点的方向（叉积）
double direction(const Point& p, const Point& q, const Point& r) {
  return (r.x() - p.x()) * (q.y() - p.y()) - (q.x() - p.x()) * (r.y() - p.y());
}

// 检查点是否在线段上
bool on_segment(const Point& p, const Point& q, const Point& r) {
  return (std::max(p.x(), r.x()) >= q.x() && q.x() >= std::min(p.x(), r.x()) &&
          std::max(p.y(), r.y()) >= q.y() && q.y() >= std::min(p.y(), r.y()));
}

bool segments_intersect(const std::pair<Point, Point>& segment1, const std::pair<Point, Point>& segment2) {
  const Point& p1 = segment1.first;
  const Point& p2 = segment1.second;
  const Point& p3 = segment2.first;
  const Point& p4 = segment2.second;

  double d1 = direction(p3, p4, p1);
  double d2 = direction(p3, p4, p2);
  double d3 = direction(p1, p2, p3);
  double d4 = direction(p1, p2, p4);

  // 如果线段互相穿过
  if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
      ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
    return true;
      }

  // 处理共线情况
  if (d1 == 0 && on_segment(p3, p1, p4)) return true;
  if (d2 == 0 && on_segment(p3, p2, p4)) return true;
  if (d3 == 0 && on_segment(p1, p3, p2)) return true;
  if (d4 == 0 && on_segment(p1, p4, p2)) return true;

  return false;
}

bool polygons_intersect(const Polygon& polygon1, const Polygon& polygon2) {
  // 检查一个多边形的点是否在另一个多边形内
  for (const auto& point : polygon1) {
    if (is_point_in_polygon(point, polygon2)) {
      return true;
    }
  }

  for (const auto& point : polygon2) {
    if (is_point_in_polygon(point, polygon1)) {
      return true;
    }
  }

  // 检查边是否相交
  auto  n1 = polygon1.size(), n2 = polygon2.size();
  for (int i = 0; i < n1; ++i) {
    std::pair<Point, Point> edge1 = {polygon1[i], polygon1[(i + 1) % n1]};
    for (int j = 0; j < n2; ++j) {
      std::pair<Point, Point> edge2 = {polygon2[j], polygon2[(j + 1) % n2]};
      if (segments_intersect(edge1, edge2)) {
        return true;
      }
    }
  }

  return false;
}

double min_distance_to_polygon(const Polygon& polygon1, const Polygon& polygon2) {
  // 如果多边形相交，距离为0
  if (polygons_intersect(polygon1, polygon2)) {
    return 0.0;
  }

  double min_dist = std::numeric_limits<double>::infinity();

  // 计算polygon1的每个点到polygon2的最小距离
  for (const auto& point : polygon1) {
    auto distance = distance_to_polygon(point, polygon2);
    auto dist = std::get<0>(distance);
    min_dist = std::min(min_dist, dist);
  }

  // 计算polygon2的每个点到polygon1的最小距离
  for (const auto& point : polygon2) {
    auto distance = distance_to_polygon(point, polygon1);
    auto dist = std::get<0>(distance);
    min_dist = std::min(min_dist, dist);
  }

  return min_dist;
}
}