#include <iostream>
#include <memory>
#include <vector>
#include <chrono>

#include <cstdlib>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#include "force_directed.h"
#include "force_model.h"
#include "graph.h"
#include "vector2d.h"
#include "svg_visualizer.h"

using namespace force_directed;

// 创建一个重叠的示例图来测试力导向算法
Graph CreateOverlappingGraph() {
    Graph graph;

    std::cout << "Creating overlapping graph with 8 nodes..." << std::endl;

    // 创建8个节点，全部重叠在中心位置
    std::vector<std::shared_ptr<Node>> nodes;

    // 中心节点
    auto center = std::make_shared<Node>();
    center->id = "Center";
    center->position = Vector2D{0.0, 0.0};  // 中心位置
    center->width = 25.0;
    center->height = 25.0;
    center->type = Node::Type::kCircle;
    nodes.push_back(center);
    graph.add_node(center);

    // 其他节点都重叠在中心附近
    for (int i = 0; i < 7; ++i) {
        auto node = std::make_shared<Node>();
        node->id = "Node_" + std::to_string(i + 1);

        // 所有节点都在中心附近，稍微随机偏移
        double small_offset = 5.0;  // 很小的偏移
        node->position = Vector2D{
            (i % 3 - 1) * small_offset,  // -5, 0, 5的偏移
            (i % 2) * small_offset       // 0, 5的偏移
        };

        node->width = 15.0 + (i % 3) * 5.0;   // 15-25的宽度
        node->height = 12.0 + (i % 2) * 6.0;  // 12-18的高度
        node->type = (i % 2 == 0) ? Node::Type::kRect : Node::Type::kCircle;

        nodes.push_back(node);
        graph.add_node(node);

        std::cout << "  " << node->id << " at (" << node->position.x()
                  << ", " << node->position.y() << ") - OVERLAPPING!" << std::endl;
    }
    
    // 创建连接关系
    std::vector<std::pair<int, int>> connections = {
        // 中心连接所有节点
        {0, 1}, {0, 2}, {0, 3}, {0, 4}, {0, 5}, {0, 6}, {0, 7},
        // 环形连接
        {1, 2}, {2, 3}, {3, 4}, {4, 5}, {5, 6}, {6, 7}, {7, 1},
        // 一些交叉连接
        {1, 4}, {2, 5}, {3, 6}
    };
    
    std::cout << "\nCreating " << connections.size() << " connections..." << std::endl;
    for (const auto& conn : connections) {
        auto edge = std::make_shared<Edge>();
        edge->id = "Edge_" + std::to_string(conn.first) + "_" + std::to_string(conn.second);
        edge->source = nodes[conn.first];
        edge->target = nodes[conn.second];
        graph.add_edge(edge);
    }
    
    return graph;
}

// 创建一个完全重叠的极端测试用例
Graph CreateExtremeOverlapGraph() {
    Graph graph;

    std::cout << "Creating extreme overlap graph with 10 nodes..." << std::endl;
    std::cout << "All nodes will be at exactly the same position!" << std::endl;

    // 创建10个节点，全部在完全相同的位置
    std::vector<std::shared_ptr<Node>> nodes;

    for (int i = 0; i < 10; ++i) {
        auto node = std::make_shared<Node>();
        node->id = "Component_" + std::to_string(i + 1);

        // 所有节点都在完全相同的位置
        node->position = Vector2D{0.0, 0.0};

        // 不同的尺寸
        node->width = 10.0 + (i % 4) * 5.0;   // 10, 15, 20, 25
        node->height = 8.0 + (i % 3) * 4.0;   // 8, 12, 16
        node->type = (i % 2 == 0) ? Node::Type::kRect : Node::Type::kCircle;

        nodes.push_back(node);
        graph.add_node(node);

        std::cout << "  " << node->id << " at (0, 0) size: "
                  << node->width << "x" << node->height << std::endl;
    }

    // 创建密集的连接关系
    std::vector<std::pair<int, int>> connections;

    // 星形连接：第一个节点连接所有其他节点
    for (int i = 1; i < 10; ++i) {
        connections.push_back({0, i});
    }

    // 环形连接
    for (int i = 1; i < 9; ++i) {
        connections.push_back({i, i + 1});
    }
    connections.push_back({9, 1}); // 闭合环

    // 一些随机交叉连接
    connections.push_back({2, 7});
    connections.push_back({3, 8});
    connections.push_back({4, 9});
    connections.push_back({1, 6});

    std::cout << "\nCreating " << connections.size() << " connections..." << std::endl;
    for (const auto& conn : connections) {
        auto edge = std::make_shared<Edge>();
        edge->id = "Edge_" + std::to_string(conn.first) + "_" + std::to_string(conn.second);
        edge->source = nodes[conn.first];
        edge->target = nodes[conn.second];
        graph.add_edge(edge);
    }

    std::cout << "Created extreme overlap test case with " << nodes.size()
              << " nodes and " << connections.size() << " edges" << std::endl;

    return graph;
}

// 运行可视化布局过程
void RunVisualizationDemo(int test_case = 2) {
    std::cout << "=== Force Directed Layout Visualization Demo ===" << std::endl;

    // 1. 创建图（根据测试用例选择）
    Graph graph;
    std::string case_name;

    switch (test_case) {
        case 1:
            graph = CreateOverlappingGraph();
            case_name = "overlapping";
            std::cout << "Using overlapping test case" << std::endl;
            break;
        case 2:
            graph = CreateExtremeOverlapGraph();
            case_name = "extreme_overlap";
            std::cout << "Using extreme overlap test case" << std::endl;
            break;
        default:
            graph = CreateOverlappingGraph();
            case_name = "overlapping";
            std::cout << "Using default overlapping test case" << std::endl;
            break;
    }
    
    // 2. 可视化初始状态
    std::cout << "\nVisualizing initial state..." << std::endl;
    std::string initial_file = "initial_layout_" + case_name + ".svg";
    visualize_graph_simple(graph, initial_file);
    
    // 3. 配置算法
    ForceDirected force_directed(graph);
    
    Config config;
    config.iterations = 100;
    config.damping = 0.9;
    config.max_velocity = 20.0;
    config.temperature = 1.0;
    config.cooling_rate = 0.995;
    config.energy_threshold = 1e-4;
    
    force_directed.set_config(config);
    
    // 4. 添加力模型
    auto repulsive = std::make_unique<RepulsiveForce>();
    repulsive->set_k_repulsive(3000.0);
    force_directed.AddForceModel(std::move(repulsive));
    
    auto attractive = std::make_unique<AttractiveForce>();
    attractive->set_k_attractive(0.15);
    force_directed.AddForceModel(std::move(attractive));
    
    // 5. 收集布局过程快照
    std::cout << "\nRunning layout algorithm and collecting snapshots..." << std::endl;
    std::vector<Graph> snapshots;
    
    // 保存初始状态
    snapshots.push_back(force_directed.graph());
    
    // 运行算法并定期保存快照
    for (int i = 0; i < config.iterations; i += 10) {
        // 运行10次迭代
        Config temp_config = config;
        temp_config.iterations = 10;
        ForceDirected temp_fd(force_directed.graph());
        temp_fd.set_config(temp_config);
        
        auto temp_repulsive = std::make_unique<RepulsiveForce>();
        temp_repulsive->set_k_repulsive(3000.0);
        temp_fd.AddForceModel(std::move(temp_repulsive));
        
        auto temp_attractive = std::make_unique<AttractiveForce>();
        temp_attractive->set_k_attractive(0.15);
        temp_fd.AddForceModel(std::move(temp_attractive));
        
        temp_fd.Run();
        
        // 更新主算法的图
        force_directed.set_graph(temp_fd.graph());
        
        // 保存快照
        snapshots.push_back(temp_fd.graph());
        
        std::cout << "Collected snapshot " << (snapshots.size() - 1) << std::endl;
    }
    
    // 6. 可视化最终状态
    std::cout << "\nVisualizing final state..." << std::endl;
    Graph final_graph = force_directed.graph();
    std::string final_file = "final_layout_" + case_name + ".svg";
    visualize_graph_simple(final_graph, final_file);

    // 7. 可视化带力向量的最终状态
    std::cout << "Visualizing final state with force vectors..." << std::endl;
    std::string forces_file = "final_layout_with_forces_" + case_name + ".svg";
    visualize_with_forces(final_graph, forces_file);

    // 8. 创建动画
    std::cout << "\nCreating layout animation..." << std::endl;
    SVGVisualizer visualizer;
    std::string animation_base = "layout_process_" + case_name;
    visualizer.visualize_layout_process(snapshots, animation_base);

    // 9. 创建自定义可视化
    std::cout << "Creating custom visualization..." << std::endl;
    SVGVisualizer custom_visualizer;
    auto& custom_config = custom_visualizer.config();

    // 自定义样式
    custom_config.canvas_width = 1000;
    custom_config.canvas_height = 800;
    custom_config.node_fill_color = Color{255, 200, 100, 0.8};
    custom_config.node_stroke_color = Color{200, 150, 50};
    custom_config.edge_color = Color{50, 50, 150};
    custom_config.edge_width = 2.0;
    custom_config.show_grid = true;
    custom_config.font_size = 14;

    std::string custom_file = "custom_layout_" + case_name + ".svg";
    custom_visualizer.visualize_graph(final_graph, custom_file);
    
    // 10. 分析结果
    std::cout << "\n=== Visualization Results ===" << std::endl;
    std::cout << "Generated files for test case '" << case_name << "':" << std::endl;
    std::cout << "  - " << initial_file << " (Initial overlapping state)" << std::endl;
    std::cout << "  - " << final_file << " (Final optimized state)" << std::endl;
    std::cout << "  - " << forces_file << " (Final state with force vectors)" << std::endl;
    std::cout << "  - " << custom_file << " (Custom styled final state)" << std::endl;
    std::cout << "  - " << animation_base << "_animation.html (Interactive animation)" << std::endl;
    std::cout << "  - " << animation_base << "_frame_*.svg (Animation frames)" << std::endl;
    
    std::cout << "\nTo view the results:" << std::endl;
    std::cout << "1. Open any .svg file in a web browser or SVG viewer" << std::endl;
    std::cout << "2. Open layout_process_animation.html in a web browser for interactive animation" << std::endl;
    
    // 计算一些统计信息
    auto nodes = final_graph.nodes();
    double total_movement = 0.0;
    auto initial_nodes = graph.nodes();
    
    for (size_t i = 0; i < nodes.size(); ++i) {
        double movement = nodes[i]->position.DistanceTo(initial_nodes[i]->position);
        total_movement += movement;
    }
    
    std::cout << "\nLayout Statistics:" << std::endl;
    std::cout << "  Nodes: " << nodes.size() << std::endl;
    std::cout << "  Edges: " << final_graph.edges().size() << std::endl;
    std::cout << "  Average movement: " << (total_movement / nodes.size()) << " units" << std::endl;
    std::cout << "  Animation frames: " << snapshots.size() << std::endl;
}

int main(int argc, char* argv[]) {
    try {
        int test_case = 2; // 默认使用极端重叠测试

        if (argc > 1) {
            test_case = std::atoi(argv[1]);
        }

        std::cout << "Available test cases:" << std::endl;
        std::cout << "  1 - Overlapping nodes (moderate overlap)" << std::endl;
        std::cout << "  2 - Extreme overlap (all nodes at same position)" << std::endl;
        std::cout << "Using test case: " << test_case << std::endl;
        std::cout << std::endl;

        RunVisualizationDemo(test_case);

        std::cout << "\n=== Visualization Demo Complete ===" << std::endl;
        std::cout << "Tip: You can run with different test cases:" << std::endl;
        std::cout << "  ./VisualizationExample 1  (moderate overlap)" << std::endl;
        std::cout << "  ./VisualizationExample 2  (extreme overlap)" << std::endl;

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
