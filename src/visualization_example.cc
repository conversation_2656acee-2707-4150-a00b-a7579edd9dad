#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#include "force_directed.h"
#include "force_model.h"
#include "graph.h"
#include "vector2d.h"
#include "svg_visualizer.h"

using namespace force_directed;

// 创建一个更复杂的示例图
Graph CreateComplexGraph() {
    Graph graph;
    
    std::cout << "Creating complex graph with 8 nodes..." << std::endl;
    
    // 创建8个节点，形成一个更复杂的网络
    std::vector<std::shared_ptr<Node>> nodes;
    
    // 中心节点
    auto center = std::make_shared<Node>();
    center->id = "Center";
    center->position = Vector2D{0.0, 0.0};
    center->width = 25.0;
    center->height = 25.0;
    center->type = Node::Type::kCircle;
    nodes.push_back(center);
    graph.add_node(center);
    
    // 周围节点
    for (int i = 0; i < 7; ++i) {
        auto node = std::make_shared<Node>();
        node->id = "Node_" + std::to_string(i + 1);
        
        // 圆形分布
        double angle = 2.0 * M_PI * i / 7.0;
        double radius = 80.0;
        node->position = Vector2D{
            radius * std::cos(angle),
            radius * std::sin(angle)
        };
        
        node->width = 15.0 + (i % 3) * 5.0;
        node->height = 12.0 + (i % 2) * 6.0;
        node->type = (i % 2 == 0) ? Node::Type::kRect : Node::Type::kCircle;
        
        nodes.push_back(node);
        graph.add_node(node);
        
        std::cout << "  " << node->id << " at (" << node->position.x() 
                  << ", " << node->position.y() << ")" << std::endl;
    }
    
    // 创建连接关系
    std::vector<std::pair<int, int>> connections = {
        // 中心连接所有节点
        {0, 1}, {0, 2}, {0, 3}, {0, 4}, {0, 5}, {0, 6}, {0, 7},
        // 环形连接
        {1, 2}, {2, 3}, {3, 4}, {4, 5}, {5, 6}, {6, 7}, {7, 1},
        // 一些交叉连接
        {1, 4}, {2, 5}, {3, 6}
    };
    
    std::cout << "\nCreating " << connections.size() << " connections..." << std::endl;
    for (const auto& conn : connections) {
        auto edge = std::make_shared<Edge>();
        edge->id = "Edge_" + std::to_string(conn.first) + "_" + std::to_string(conn.second);
        edge->source = nodes[conn.first];
        edge->target = nodes[conn.second];
        graph.add_edge(edge);
    }
    
    return graph;
}

// 运行可视化布局过程
void RunVisualizationDemo() {
    std::cout << "=== Force Directed Layout Visualization Demo ===" << std::endl;
    
    // 1. 创建图
    Graph graph = CreateComplexGraph();
    
    // 2. 可视化初始状态
    std::cout << "\nVisualizing initial state..." << std::endl;
    visualize_graph_simple(graph, "initial_layout.svg");
    
    // 3. 配置算法
    ForceDirected force_directed(graph);
    
    Config config;
    config.iterations = 100;
    config.damping = 0.9;
    config.max_velocity = 20.0;
    config.temperature = 1.0;
    config.cooling_rate = 0.995;
    config.energy_threshold = 1e-4;
    
    force_directed.set_config(config);
    
    // 4. 添加力模型
    auto repulsive = std::make_unique<RepulsiveForce>();
    repulsive->set_k_repulsive(3000.0);
    force_directed.AddForceModel(std::move(repulsive));
    
    auto attractive = std::make_unique<AttractiveForce>();
    attractive->set_k_attractive(0.15);
    force_directed.AddForceModel(std::move(attractive));
    
    // 5. 收集布局过程快照
    std::cout << "\nRunning layout algorithm and collecting snapshots..." << std::endl;
    std::vector<Graph> snapshots;
    
    // 保存初始状态
    snapshots.push_back(force_directed.graph());
    
    // 运行算法并定期保存快照
    for (int i = 0; i < config.iterations; i += 10) {
        // 运行10次迭代
        Config temp_config = config;
        temp_config.iterations = 10;
        ForceDirected temp_fd(force_directed.graph());
        temp_fd.set_config(temp_config);
        
        auto temp_repulsive = std::make_unique<RepulsiveForce>();
        temp_repulsive->set_k_repulsive(3000.0);
        temp_fd.AddForceModel(std::move(temp_repulsive));
        
        auto temp_attractive = std::make_unique<AttractiveForce>();
        temp_attractive->set_k_attractive(0.15);
        temp_fd.AddForceModel(std::move(temp_attractive));
        
        temp_fd.Run();
        
        // 更新主算法的图
        force_directed.set_graph(temp_fd.graph());
        
        // 保存快照
        snapshots.push_back(temp_fd.graph());
        
        std::cout << "Collected snapshot " << (snapshots.size() - 1) << std::endl;
    }
    
    // 6. 可视化最终状态
    std::cout << "\nVisualizing final state..." << std::endl;
    Graph final_graph = force_directed.graph();
    visualize_graph_simple(final_graph, "final_layout.svg");
    
    // 7. 可视化带力向量的最终状态
    std::cout << "Visualizing final state with force vectors..." << std::endl;
    visualize_with_forces(final_graph, "final_layout_with_forces.svg");
    
    // 8. 创建动画
    std::cout << "\nCreating layout animation..." << std::endl;
    SVGVisualizer visualizer;
    visualizer.visualize_layout_process(snapshots, "layout_process");
    
    // 9. 创建自定义可视化
    std::cout << "Creating custom visualization..." << std::endl;
    SVGVisualizer custom_visualizer;
    auto& custom_config = custom_visualizer.config();
    
    // 自定义样式
    custom_config.canvas_width = 1000;
    custom_config.canvas_height = 800;
    custom_config.node_fill_color = Color{255, 200, 100, 0.8};
    custom_config.node_stroke_color = Color{200, 150, 50};
    custom_config.edge_color = Color{50, 50, 150};
    custom_config.edge_width = 2.0;
    custom_config.show_grid = true;
    custom_config.font_size = 14;
    
    custom_visualizer.visualize_graph(final_graph, "custom_layout.svg");
    
    // 10. 分析结果
    std::cout << "\n=== Visualization Results ===" << std::endl;
    std::cout << "Generated files:" << std::endl;
    std::cout << "  - initial_layout.svg (Initial state)" << std::endl;
    std::cout << "  - final_layout.svg (Final state)" << std::endl;
    std::cout << "  - final_layout_with_forces.svg (Final state with force vectors)" << std::endl;
    std::cout << "  - custom_layout.svg (Custom styled final state)" << std::endl;
    std::cout << "  - layout_process_animation.html (Interactive animation)" << std::endl;
    std::cout << "  - layout_process_frame_*.svg (Animation frames)" << std::endl;
    
    std::cout << "\nTo view the results:" << std::endl;
    std::cout << "1. Open any .svg file in a web browser or SVG viewer" << std::endl;
    std::cout << "2. Open layout_process_animation.html in a web browser for interactive animation" << std::endl;
    
    // 计算一些统计信息
    auto nodes = final_graph.nodes();
    double total_movement = 0.0;
    auto initial_nodes = graph.nodes();
    
    for (size_t i = 0; i < nodes.size(); ++i) {
        double movement = nodes[i]->position.DistanceTo(initial_nodes[i]->position);
        total_movement += movement;
    }
    
    std::cout << "\nLayout Statistics:" << std::endl;
    std::cout << "  Nodes: " << nodes.size() << std::endl;
    std::cout << "  Edges: " << final_graph.edges().size() << std::endl;
    std::cout << "  Average movement: " << (total_movement / nodes.size()) << " units" << std::endl;
    std::cout << "  Animation frames: " << snapshots.size() << std::endl;
}

int main() {
    try {
        RunVisualizationDemo();
        std::cout << "\n=== Visualization Demo Complete ===" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
