#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <algorithm>
#include <cmath>

#include "force_directed.h"
#include "force_model.h"
#include "graph.h"
#include "vector2d.h"

using namespace force_directed;

// 创建一个简单的示例图
Graph CreateExampleGraph() {
    Graph graph;
    
    std::cout << "Creating example graph with 6 nodes..." << std::endl;
    
    // 创建6个节点，形成一个小型网络
    std::vector<std::shared_ptr<Node>> nodes;
    for (int i = 0; i < 6; ++i) {
        auto node = std::make_shared<Node>();
        node->id = "Component_" + std::to_string(i + 1);
        
        // 随机初始位置
        node->position = Vector2D{
            static_cast<double>(i * 30 + (i % 2) * 15), 
            static_cast<double>((i / 2) * 25)
        };
        
        // 设置组件尺寸
        node->width = 10.0 + (i % 3) * 5.0;  // 10-20的宽度
        node->height = 8.0 + (i % 2) * 4.0;  // 8-12的高度
        
        nodes.push_back(node);
        graph.add_node(node);
        
        std::cout << "  " << node->id << " at (" << node->position.x() 
                  << ", " << node->position.y() << ") size: " 
                  << node->width << "x" << node->height << std::endl;
    }
    
    // 创建连接关系，模拟电路板上的连线
    std::vector<std::pair<int, int>> connections = {
        {0, 1}, {1, 2}, {2, 3}, {3, 4}, {4, 5}, {5, 0}, // 环形连接
        {0, 3}, {1, 4}  // 交叉连接
    };
    
    std::cout << "\nCreating connections:" << std::endl;
    for (const auto& conn : connections) {
        auto edge = std::make_shared<Edge>();
        edge->id = "Edge_" + std::to_string(conn.first) + "_" + std::to_string(conn.second);
        edge->source = nodes[conn.first];
        edge->target = nodes[conn.second];
        graph.add_edge(edge);
        
        std::cout << "  " << nodes[conn.first]->id << " <-> " 
                  << nodes[conn.second]->id << std::endl;
    }
    
    return graph;
}

// 打印图的当前状态
void PrintGraphState(const Graph& graph, const std::string& title) {
    std::cout << "\n=== " << title << " ===" << std::endl;
    
    auto nodes = graph.nodes();
    for (const auto& node : nodes) {
        std::cout << node->id << ": position(" << node->position.x() 
                  << ", " << node->position.y() << "), force(" 
                  << node->forces.x() << ", " << node->forces.y() 
                  << "), |force|=" << node->forces.Magnitude() << std::endl;
    }
}

// 计算图的总能量
double CalculateTotalEnergy(const Graph& graph) {
    double total_energy = 0.0;
    
    // 计算斥力能量
    RepulsiveForce repulsive;
    total_energy += repulsive.Energy(graph);
    
    // 计算引力能量
    AttractiveForce attractive;
    total_energy += attractive.Energy(graph);
    
    return total_energy;
}

int main() {
    std::cout << "=== Force Directed Layout Example ===" << std::endl;
    
    // 1. 创建示例图
    Graph graph = CreateExampleGraph();
    
    // 2. 记录初始位置
    auto nodes = graph.nodes();
    std::vector<Vector2D> initial_positions;
    for (const auto& node : nodes) {
        initial_positions.push_back(node->position);
    }

    // 打印初始状态
    PrintGraphState(graph, "Initial State");
    double initial_energy = CalculateTotalEnergy(graph);
    std::cout << "Initial total energy: " << initial_energy << std::endl;
    
    // 3. 配置力导向算法
    std::cout << "\n=== Configuring Force Directed Algorithm ===" << std::endl;
    
    ForceDirected force_directed(graph);
    
    // 设置算法参数
    Config config;
    config.iterations = 200;           // 迭代次数
    config.damping = 0.9;             // 阻尼系数
    config.max_velocity = 15.0;       // 最大速度
    config.temperature = 1.0;         // 初始温度
    config.cooling_rate = 0.995;      // 冷却速率
    config.energy_threshold = 1e-4;   // 收敛阈值
    
    force_directed.set_config(config);
    
    std::cout << "Algorithm configuration:" << std::endl;
    std::cout << "  Iterations: " << config.iterations << std::endl;
    std::cout << "  Damping: " << config.damping << std::endl;
    std::cout << "  Max velocity: " << config.max_velocity << std::endl;
    std::cout << "  Temperature: " << config.temperature << std::endl;
    std::cout << "  Cooling rate: " << config.cooling_rate << std::endl;
    std::cout << "  Energy threshold: " << config.energy_threshold << std::endl;
    
    // 4. 添加力模型
    std::cout << "\nAdding force models..." << std::endl;
    
    // 添加斥力模型
    auto repulsive = std::make_unique<RepulsiveForce>();
    repulsive->set_k_repulsive(2000.0);  // 斥力系数
    std::cout << "  Repulsive force (k=" << 2000.0 << ")" << std::endl;
    force_directed.AddForceModel(std::move(repulsive));
    
    // 添加引力模型
    auto attractive = std::make_unique<AttractiveForce>();
    attractive->set_k_attractive(0.1);   // 引力系数
    std::cout << "  Attractive force (k=" << 0.1 << ")" << std::endl;
    force_directed.AddForceModel(std::move(attractive));
    
    // 5. 运行算法
    std::cout << "\n=== Running Force Directed Layout ===" << std::endl;
    std::cout << "Starting layout optimization..." << std::endl;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    force_directed.Run();
    auto end_time = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "Layout completed in " << duration.count() << " ms" << std::endl;
    
    // 6. 打印最终状态
    Graph final_graph = force_directed.graph();
    PrintGraphState(final_graph, "Final State");
    
    double final_energy = CalculateTotalEnergy(final_graph);
    std::cout << "Final total energy: " << final_energy << std::endl;
    std::cout << "Energy reduction: " << (initial_energy - final_energy) 
              << " (" << ((initial_energy - final_energy) / initial_energy * 100) 
              << "%)" << std::endl;
    
    // 7. 分析结果
    std::cout << "\n=== Layout Analysis ===" << std::endl;

    auto final_nodes = final_graph.nodes();
    double total_movement = 0.0;
    double max_movement = 0.0;

    for (size_t i = 0; i < final_nodes.size(); ++i) {
        double movement = final_nodes[i]->position.DistanceTo(initial_positions[i]);
        total_movement += movement;
        max_movement = std::max(max_movement, movement);

        std::cout << final_nodes[i]->id << " moved " << movement << " units" << std::endl;
    }
    
    std::cout << "Average movement: " << (total_movement / final_nodes.size()) << " units" << std::endl;
    std::cout << "Maximum movement: " << max_movement << " units" << std::endl;
    
    // 8. 检查重叠
    std::cout << "\n=== Overlap Check ===" << std::endl;
    int overlap_count = 0;
    for (size_t i = 0; i < final_nodes.size(); ++i) {
        for (size_t j = i + 1; j < final_nodes.size(); ++j) {
            const auto& node1 = final_nodes[i];
            const auto& node2 = final_nodes[j];
            
            double dx = std::abs(node1->position.x() - node2->position.x());
            double dy = std::abs(node1->position.y() - node2->position.y());
            double min_dx = (node1->width + node2->width) / 2.0;
            double min_dy = (node1->height + node2->height) / 2.0;
            
            if (dx < min_dx && dy < min_dy) {
                overlap_count++;
                std::cout << "Overlap detected: " << node1->id << " and " << node2->id << std::endl;
            }
        }
    }
    
    if (overlap_count == 0) {
        std::cout << "No overlaps detected - layout is valid!" << std::endl;
    } else {
        std::cout << "Found " << overlap_count << " overlaps" << std::endl;
    }
    
    std::cout << "\n=== Example Complete ===" << std::endl;
    
    return 0;
}
