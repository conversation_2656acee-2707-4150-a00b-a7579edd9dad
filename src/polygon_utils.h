#ifndef POLYGON_UTILS_H
#define POLYGON_UTILS_H

#include <vector>
#include <utility>
#include <tuple>
#include "vector2d.h"

namespace force_directed {

using Point = Vector2D;
using Polygon = std::vector<Point>;

/**
 * 射线法判断点是否在多边形内部
 */
bool is_point_in_polygon(const Point& point, const Polygon& polygon);

/**
 * 计算点到线段的最短距离和最近点
 * @return (distance, closest_point)
 */
std::pair<double, Point> distance_to_segment(const Point& point, const std::pair<Point, Point>& segment);

/**
 * 计算点到多边形的最短距离、最近点和方向向量
 * @return (distance, closest_point, direction)
 */
std::tuple<double, Point, Vector2D> distance_to_polygon(const Point& point, const Polygon& polygon);

/**
 * 计算多边形的中心点
 */
Point calculate_polygon_centroid(const Polygon& polygon);

/**
 * 判断两个多边形是否相交
 */
bool polygons_intersect(const Polygon& polygon1, const Polygon& polygon2);

/**
 * 判断两条线段是否相交
 */
bool segments_intersect(const std::pair<Point, Point>& segment1, const std::pair<Point, Point>& segment2);

/**
 * 计算两个多边形之间的最小距离
 */
double min_distance_to_polygon(const Polygon& polygon1, const Polygon& polygon2);

}

#endif // POLYGON_UTILS_H
