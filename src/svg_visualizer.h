#ifndef SVG_VISUALIZER_H
#define SVG_VISUALIZER_H

#include <string>
#include <vector>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <tuple>

#include "graph.h"
#include "vector2d.h"

namespace force_directed {

struct Color {
    int r, g, b;
    double alpha;
    
    Color(int red = 0, int green = 0, int blue = 0, double a = 1.0) 
        : r(red), g(green), b(blue), alpha(a) {}
    
    std::string to_string() const {
        if (alpha < 1.0) {
            return "rgba(" + std::to_string(r) + "," + std::to_string(g) + "," + 
                   std::to_string(b) + "," + std::to_string(alpha) + ")";
        }
        return "rgb(" + std::to_string(r) + "," + std::to_string(g) + "," + std::to_string(b) + ")";
    }
};

struct BoundingBox {
    double min_x, min_y, max_x, max_y;
    
    BoundingBox() : min_x(1e9), min_y(1e9), max_x(-1e9), max_y(-1e9) {}
    
    void update(double x, double y) {
        min_x = std::min(min_x, x);
        min_y = std::min(min_y, y);
        max_x = std::max(max_x, x);
        max_y = std::max(max_y, y);
    }
    
    double width() const { return max_x - min_x; }
    double height() const { return max_y - min_y; }
    Vector2D center() const { return Vector2D{(min_x + max_x) / 2.0, (min_y + max_y) / 2.0}; }
};

class SVGVisualizer {
public:
    struct VisualizationConfig {
        // 画布设置
        int canvas_width = 800;
        int canvas_height = 600;
        double margin = 50.0;
        
        // 节点样式
        Color node_fill_color{100, 150, 255, 0.8};
        Color node_stroke_color{50, 100, 200};
        double node_stroke_width = 2.0;
        
        // 边样式
        Color edge_color{100, 100, 100};
        double edge_width = 1.5;
        
        // 力向量样式
        Color force_color{255, 100, 100, 0.7};
        double force_width = 1.0;
        double force_scale = 0.1;  // 力向量显示缩放
        
        // 文本样式
        Color text_color{0, 0, 0};
        int font_size = 12;
        
        // 显示选项
        bool show_nodes = true;
        bool show_edges = true;
        bool show_forces = false;
        bool show_node_labels = true;
        bool show_grid = false;
        bool show_board_boundary = false;
        bool auto_fit = true;

        // 板框样式
        Color board_boundary_color{200, 100, 100};
        double board_boundary_width = 3.0;
    };

    SVGVisualizer();
    explicit SVGVisualizer(const VisualizationConfig& config);
    
    // 主要接口
    void visualize_graph(const Graph& graph, const std::string& filename);
    void visualize_graph_with_board(const Graph& graph, const std::vector<Vector2D>& board_boundary,
                                   const std::string& filename);
    void visualize_layout_process(const std::vector<Graph>& snapshots,
                                 const std::string& base_filename);
    
    // 配置接口
    void set_config(const VisualizationConfig& config) { config_ = config; }
    VisualizationConfig& config() { return config_; }
    
    // 静态工具方法
    static void create_animation(const std::vector<std::string>& svg_files,
                               const std::string& output_html,
                               double frame_duration = 100.0);

private:
    VisualizationConfig config_;
    std::vector<Vector2D> board_boundary_;

    // 内部方法
    BoundingBox calculate_bounding_box(const Graph& graph) const;
    BoundingBox calculate_bounding_box_with_board(const Graph& graph, const std::vector<Vector2D>& board_boundary) const;
    std::tuple<double, double, double> calculate_transform(const BoundingBox& bbox) const;
    
    void write_svg_header(std::ofstream& file, const BoundingBox& bbox) const;
    void write_svg_footer(std::ofstream& file) const;
    
    void draw_grid(std::ofstream& file, const BoundingBox& bbox, 
                   double scale, double offset_x, double offset_y) const;
    void draw_edges(std::ofstream& file, const Graph& graph,
                   double scale, double offset_x, double offset_y) const;
    void draw_nodes(std::ofstream& file, const Graph& graph,
                   double scale, double offset_x, double offset_y) const;
    void draw_forces(std::ofstream& file, const Graph& graph,
                    double scale, double offset_x, double offset_y) const;
    void draw_labels(std::ofstream& file, const Graph& graph,
                    double scale, double offset_x, double offset_y) const;
    void draw_board_boundary(std::ofstream& file, const std::vector<Vector2D>& boundary,
                           double scale, double offset_x, double offset_y) const;
    
    // 坐标转换
    double transform_x(double x, double scale, double offset_x) const;
    double transform_y(double y, double scale, double offset_y) const;
    
    // 工具方法
    std::string escape_xml(const std::string& text) const;
    Color interpolate_color(const Color& c1, const Color& c2, double t) const;
};

// 便利函数
void visualize_graph_simple(const Graph& graph, const std::string& filename);
void visualize_with_forces(const Graph& graph, const std::string& filename);
void visualize_graph_with_board(const Graph& graph, const std::vector<Vector2D>& board_boundary, const std::string& filename);

} // namespace force_directed

#endif // SVG_VISUALIZER_H
