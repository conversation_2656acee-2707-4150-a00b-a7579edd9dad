#ifndef FORCELAYOUT_VECTOR_H
#define FORCELAYOUT_VECTOR_H

#include <iostream>

namespace force_directed {

class Vector2D {
 public:
  Vector2D() : x_(0.0), y_(0.0) {}
  Vector2D(const double x, const double y) : x_(x), y_(y) {}
  Vector2D(const Vector2D& other) = default;
  Vector2D& operator=(const Vector2D& other) = default;

  double x() const { return x_; }
  double y() const { return y_; }
  void set_x(const double x) { x_ = x; }
  void set_y(const double y) { y_ = y; }
  void Set(const double x, const double y) {
    x_ = x;
    y_ = y;
  }

  Vector2D operator+(const Vector2D& other) const {
    return {x_ + other.x_, y_ + other.y_};
  }

  Vector2D& operator+=(const Vector2D& other) {
    x_ += other.x_;
    y_ += other.y_;
    return *this;
  }

  Vector2D operator-(const Vector2D& other) const {
    return {x_ - other.x_, y_ - other.y_};
  }

  Vector2D& operator-=(const Vector2D& other) {
    x_ -= other.x_;
    y_ -= other.y_;
    return *this;
  }

  Vector2D operator*(const double scalar) const {
    return {x_ * scalar, y_ * scalar};
  }

  Vector2D& operator*=(const double scalar) {
    x_ *= scalar;
    y_ *= scalar;
    return *this;
  }

  Vector2D operator/(const double scalar) const {
    return {x_ / scalar, y_ / scalar};
  }

  Vector2D& operator/=(const double scalar) {
    x_ /= scalar;
    y_ /= scalar;
    return *this;
  }

  Vector2D operator-() const { return {-x_, -y_}; }

  double Dot(const Vector2D& other) const {
    return x_ * other.x_ + y_ * other.y_;
  }

  double Cross(const Vector2D& other) const {
    return x_ * other.y_ - y_ * other.x_;
  }

  double Magnitude() const { return std::sqrt(x_ * x_ + y_ * y_); }

  double MagnitudeSquared() const { return x_ * x_ + y_ * y_; }

  Vector2D Normalized() const {
    const double mag = Magnitude();
    if (mag == 0.0) {
      return {0.0, 0.0};
    }
    return {x_ / mag, y_ / mag};
  }

  void Normalize() {
    const double mag = Magnitude();
    if (mag != 0.0) {
      x_ /= mag;
      y_ /= mag;
    }
  }

  Vector2D ProjectOnto(const Vector2D& other) const {
    const double other_mag_sq = other.MagnitudeSquared();
    if (other_mag_sq == 0.0) {
      return {0.0, 0.0};
    }
    const double projection_length = this->Dot(other) / other_mag_sq;
    return other * projection_length;
  }

  bool IsZero() const { return x_ == 0.0 && y_ == 0.0; }

  double DistanceTo(const Vector2D& other) const {
    return (*this - other).Magnitude();
  }

  double DistanceSquaredTo(const Vector2D& other) const {
    return (*this - other).MagnitudeSquared();
  }

 private:
  double x_;
  double y_;
};

inline Vector2D operator*(const double scalar, const Vector2D& vector) {
  return vector * scalar;
}

inline std::ostream& operator<<(std::ostream& os, const Vector2D& vector) {
  os << "(" << vector.x() << ", " << vector.y() << ")";
  return os;
}

inline bool operator==(const Vector2D& lhs, const Vector2D& rhs) {
  return lhs.x() == rhs.x() && lhs.y() == rhs.y();
}

inline bool operator!=(const Vector2D& lhs, const Vector2D& rhs) {
  return !(lhs == rhs);
}

}  // namespace force_directed

#endif  // FORCELAYOUT_VECTOR_H
